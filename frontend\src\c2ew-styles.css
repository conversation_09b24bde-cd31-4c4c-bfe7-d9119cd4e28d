/* Styles CSS pour l'interface C2-EW Platform */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto+Mono:wght@300;400;500;700&display=swap');

/* Variables CSS pour le thème C2-EW */
:root {
  --c2ew-primary: #000000;
  --c2ew-secondary: #111111;
  --c2ew-accent: #2563eb;
  --c2ew-success: #10b981;
  --c2ew-warning: #f59e0b;
  --c2ew-danger: #ef4444;
  --c2ew-dark: #000000;
  --c2ew-dark-card: #111111;
  --c2ew-dark-border: #333333;
  --c2ew-text: #ffffff;
  --c2ew-text-muted: #888888;
  --c2ew-lcd: #00ff41;
  --c2ew-lcd-bg: #000000;
}

/* Reset et base */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  background: var(--c2ew-dark);
  color: var(--c2ew-text);
  overflow-x: hidden;
}

/* Animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes glow {
  0% { text-shadow: 0 0 5px var(--c2ew-lcd); }
  50% { text-shadow: 0 0 20px var(--c2ew-lcd), 0 0 30px var(--c2ew-lcd); }
  100% { text-shadow: 0 0 5px var(--c2ew-lcd); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Styles pour les sliders */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--c2ew-dark-border);
  outline: none;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--c2ew-accent);
  cursor: pointer;
  border: 1px solid var(--c2ew-text);
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: var(--c2ew-success);
  box-shadow: 0 0 10px var(--c2ew-accent);
}

input[type="range"]::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--c2ew-accent);
  cursor: pointer;
  border: 1px solid var(--c2ew-text);
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  background: var(--c2ew-success);
  box-shadow: 0 0 10px var(--c2ew-accent);
}

input[type="range"]::-webkit-slider-track {
  background: var(--c2ew-dark-border);
  height: 4px;
  border-radius: 2px;
}

input[type="range"]::-moz-range-track {
  background: var(--c2ew-dark-border);
  height: 4px;
  border-radius: 2px;
}

/* Styles pour les inputs */
input[type="text"], input[type="number"] {
  background: var(--c2ew-dark-card);
  border: 1px solid var(--c2ew-dark-border);
  color: var(--c2ew-text);
  border-radius: 4px;
  padding: 6px 8px;
  font-family: 'Roboto Mono', monospace;
  font-size: 0.8rem;
  transition: border-color 0.2s ease;
}

input[type="text"]:focus, input[type="number"]:focus {
  outline: none;
  border-color: var(--c2ew-accent);
  box-shadow: 0 0 5px rgba(37, 99, 235, 0.3);
}

/* Styles pour les boutons */
button {
  font-family: 'Roboto Mono', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Styles pour l'écran LCD */
.lcd-display {
  background: var(--c2ew-lcd-bg);
  border: 2px solid var(--c2ew-dark-border);
  border-radius: 6px;
  padding: 15px;
  text-align: center;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.8);
  position: relative;
}

.lcd-frequency {
  font-family: 'Orbitron', 'Roboto Mono', monospace;
  font-size: 2rem;
  color: var(--c2ew-lcd);
  text-shadow: 0 0 8px var(--c2ew-lcd);
  margin-bottom: 8px;
  letter-spacing: 1px;
  animation: glow 2s ease-in-out infinite alternate;
}

.lcd-info {
  font-size: 0.8rem;
  color: var(--c2ew-lcd);
  opacity: 0.8;
  display: flex;
  justify-content: space-between;
}

/* Styles pour la molette rotative */
.rotary-knob {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(145deg, var(--c2ew-dark-border), var(--c2ew-secondary));
  border: 2px solid var(--c2ew-dark-border);
  cursor: pointer;
  position: relative;
  margin: 10px auto;
  transition: all 0.2s ease;
}

.rotary-knob:hover {
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.3);
}

.rotary-indicator {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 20px;
  background: var(--c2ew-accent);
  border-radius: 2px;
}

/* Styles pour les cartes */
.c2ew-card {
  background: var(--c2ew-dark-card);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid var(--c2ew-dark-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
}

.c2ew-card-title {
  font-size: 0.9rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--c2ew-text);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Styles pour les badges */
.c2ew-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.c2ew-badge-success {
  background: var(--c2ew-success);
  color: white;
}

.c2ew-badge-danger {
  background: var(--c2ew-danger);
  color: white;
}

.c2ew-badge-warning {
  background: var(--c2ew-warning);
  color: white;
}

.c2ew-badge-accent {
  background: var(--c2ew-accent);
  color: white;
}

/* Styles pour les barres de niveau */
.level-bar {
  width: 100%;
  height: 3px;
  background: var(--c2ew-dark-border);
  border-radius: 2px;
  overflow: hidden;
}

.level-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--c2ew-success) 0%, var(--c2ew-warning) 70%, var(--c2ew-danger) 100%);
  transition: width 0.1s ease;
}

/* Styles pour les scrollbars */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--c2ew-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--c2ew-dark-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--c2ew-accent);
}

/* Responsive */
@media (max-width: 768px) {
  .c2ew-main-grid {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }
  
  .lcd-frequency {
    font-size: 1.5rem !important;
  }
  
  .rotary-knob {
    width: 50px !important;
    height: 50px !important;
  }
}

<!doctype html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>C2-EW Platform - ICOM IC-R8600 Controller</title>

    <!-- Google Fonts pour l'interface C2-EW -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto+Mono:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      * {
        box-sizing: border-box;
      }

      #root {
        width: 100%;
        min-height: 100vh;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; color: white; font-size: 24px; font-family: 'Roboto Mono', monospace;">
        <div style="font-family: 'Orbitron', sans-serif; font-size: 32px; margin-bottom: 20px; font-weight: bold;">C2-EW Platform</div>
        <div>Chargement de l'interface ICOM IC-R8600...</div>
        <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.2); margin-top: 20px; border-radius: 2px; overflow: hidden; position: relative;">
          <div style="position: absolute; top: 0; left: 0; height: 100%; width: 30%; background: #3b82f6; animation: loading 1.5s infinite ease-in-out;">
          </div>
        </div>
      </div>
    </div>

    <style>
      @keyframes loading {
        0% { left: -30%; }
        100% { left: 100%; }
      }
    </style>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>

import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import '../c2ew-styles.css';
import { 
  Power, 
  Radio, 
  Volume2, 
  Settings, 
  Play, 
  Square, 
  Download,
  Trash2,
  RefreshCw,
  Zap,
  Activity,
  Target,
  Scan,
  Mic,
  MicOff,
  Plus,
  Minus,
  RotateCcw,
  Filter,
  Waves,
  Monitor,
  Headphones,
  VolumeX,
  MapPin,
  Clock,
  Signal,
  Wifi,
  WifiOff
} from 'lucide-react';

// Configuration de l'API
const API_BASE_URL = 'http://localhost:8001';

// Thème C2-EW Platform - Version compacte et sombre
const c2ewTheme = {
  colors: {
    primary: '#000000',       // Noir pur
    secondary: '#111111',     // Noir léger
    accent: '#2563eb',        // Bleu accent
    success: '#10b981',       // Vert
    warning: '#f59e0b',       // Orange
    danger: '#ef4444',        // Rouge
    dark: '#000000',          // Noir pur
    darkCard: '#111111',      // Noir léger pour cartes
    darkBorder: '#333333',    // Bordure gris foncé
    text: '#ffffff',          // Texte blanc pur
    textMuted: '#888888',     // Texte gris
    lcd: '#00ff41',           // Vert LCD vif
    lcdBg: '#000000'          // Fond LCD noir
  },
  shadows: {
    card: '0 2px 8px rgba(0, 0, 0, 0.8)',
    button: '0 1px 3px rgba(0, 0, 0, 0.5)',
    inset: 'inset 0 1px 3px rgba(0, 0, 0, 0.8)'
  }
};

// Styles CSS-in-JS avec thème C2-EW - Version compacte
const styles = {
  container: {
    background: c2ewTheme.colors.dark,
    color: c2ewTheme.colors.text,
    fontFamily: '"Roboto Mono", "Courier New", monospace',
    padding: '10px',
    minHeight: '100vh'
  },

  mainGrid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '15px',
    maxWidth: '1400px',
    margin: '0 auto'
  },

  card: {
    background: c2ewTheme.colors.darkCard,
    borderRadius: '8px',
    padding: '20px',
    boxShadow: c2ewTheme.shadows.card,
    border: `1px solid ${c2ewTheme.colors.darkBorder}`
  },

  cardTitle: {
    fontSize: '0.9rem',
    fontWeight: 'bold',
    marginBottom: '10px',
    color: c2ewTheme.colors.text,
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },

  lcdDisplay: {
    background: c2ewTheme.colors.lcdBg,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    borderRadius: '6px',
    padding: '15px',
    textAlign: 'center',
    marginBottom: '15px',
    boxShadow: c2ewTheme.shadows.inset,
    position: 'relative'
  },

  lcdFrequency: {
    fontSize: '2rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    color: c2ewTheme.colors.lcd,
    textShadow: `0 0 8px ${c2ewTheme.colors.lcd}`,
    marginBottom: '8px',
    letterSpacing: '1px',
    cursor: 'pointer'
  },

  lcdInfo: {
    fontSize: '0.8rem',
    color: c2ewTheme.colors.lcd,
    opacity: 0.8,
    display: 'flex',
    justifyContent: 'space-between'
  },

  frequencyInput: {
    background: c2ewTheme.colors.lcdBg,
    border: `1px solid ${c2ewTheme.colors.lcd}`,
    borderRadius: '4px',
    padding: '8px',
    color: c2ewTheme.colors.lcd,
    fontSize: '1.5rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    textAlign: 'center',
    width: '100%',
    marginBottom: '10px'
  },

  rotaryKnob: {
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    background: `linear-gradient(145deg, ${c2ewTheme.colors.darkBorder}, ${c2ewTheme.colors.secondary})`,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    cursor: 'pointer',
    position: 'relative',
    margin: '10px auto',
    boxShadow: c2ewTheme.shadows.button
  },

  rotaryIndicator: {
    position: 'absolute',
    top: '5px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '3px',
    height: '20px',
    background: c2ewTheme.colors.accent,
    borderRadius: '2px'
  }
};

const C2EWRadioControl = () => {
  // États pour les contrôles
  const [frequency, setFrequency] = useState(145500000);
  const [mode, setMode] = useState('FM');
  const [afGain, setAfGain] = useState(50);
  const [rfGain, setRfGain] = useState(50);
  const [filterBW, setFilterBW] = useState('2.40K');
  const [squelch, setSquelch] = useState(0);

  // États pour l'édition de fréquence
  const [isEditingFreq, setIsEditingFreq] = useState(false);
  const [frequencyInput, setFrequencyInput] = useState('145.500.000');
  const [knobRotation, setKnobRotation] = useState(0);
  
  // États pour le scan
  const [scanStartFreq, setScanStartFreq] = useState(144000000);
  const [scanEndFreq, setScanEndFreq] = useState(146000000);
  const [scanStep, setScanStep] = useState(25000);
  const [isScanning, setIsScanning] = useState(false);
  
  // États pour l'état du récepteur
  const [radioStatus, setRadioStatus] = useState({
    frequency: 145500000,
    mode: 'FM',
    rssi: -80,
    power_on: false,
    rf_gain: 50,
    af_gain: 50,
    filter_width: 2400,
    squelch: 0,
    volume: 50
  });
  
  // États pour l'enregistrement et audio
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  
  // États pour les fréquences cibles
  const [targetFrequencies, setTargetFrequencies] = useState([
    { freq: 145500000, name: 'R1 - Urgence', comment: 'Canal d\'urgence principal', active: false },
    { freq: 145750000, name: 'R2 - Trafic', comment: 'Coordination trafic', active: false },
    { freq: 146000000, name: 'R3 - Coordination', comment: 'Canal de coordination', active: false },
    { freq: 433500000, name: 'UHF - Tactique', comment: 'Fréquence tactique UHF', active: false }
  ]);
  const [newFreqInput, setNewFreqInput] = useState('');
  const [newFreqComment, setNewFreqComment] = useState('');
  
  // États pour les messages et connexion
  const [message, setMessage] = useState({ type: '', text: '' });
  const [loading, setLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState('EXCELLENT');
  
  // Références pour l'audio
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const gainNodeRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const websocketRef = useRef(null);
  const audioBufferRef = useRef([]);
  
  // Modes de modulation disponibles
  const modes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM', 'NFM'];
  
  // Filtres de bande passante
  const bandwidthFilters = ['1.80K', '2.40K', '3.00K', '6.00K', '15.0K'];

  // Fonctions utilitaires
  const formatFrequency = (freq) => {
    if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  };

  const formatFrequencyLCD = (freq) => {
    const freqStr = freq.toString().padStart(9, '0');
    return `${freqStr.slice(0, 3)}.${freqStr.slice(3, 6)}.${freqStr.slice(6, 9)}`;
  };

  const parseFrequencyInput = (input) => {
    return parseInt(input.replace(/\./g, ''));
  };

  // Gestionnaire pour l'édition directe de fréquence
  const handleFrequencyEdit = () => {
    setIsEditingFreq(true);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  const handleFrequencySubmit = () => {
    const newFreq = parseFrequencyInput(frequencyInput);
    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Mise à jour immédiate
      setFrequency(newFreq);
      setRadioStatus(prev => ({ ...prev, frequency: newFreq }));
      setIsEditingFreq(false);

      // Envoi immédiat de la commande
      sendCommand({ frequency: newFreq });
      showMessage('success', `Fréquence changée: ${formatFrequencyLCD(newFreq)} Hz`);
    } else {
      showMessage('error', 'Fréquence invalide (100kHz - 3GHz)');
    }
  };

  const handleFrequencyCancel = () => {
    setIsEditingFreq(false);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  // Gestionnaire pour la molette rotative - optimisé pour changement instantané
  const handleKnobRotation = (delta) => {
    const step = 1000; // 1kHz par cran pour plus de précision
    const currentFreq = radioStatus.frequency || frequency;
    const newFreq = currentFreq + (delta * step);

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);
      setRadioStatus(prev => ({ ...prev, frequency: newFreq }));
      setKnobRotation(prev => prev + delta * 15); // 15 degrés par cran

      // Envoi immédiat de la commande au récepteur
      sendCommand({ frequency: newFreq });
    }
  };

  // Fonction pour afficher un message
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  // Fonctions API - optimisée pour rapidité
  const sendCommand = async (commandData) => {
    try {
      // Envoi rapide sans attendre la réponse complète
      const response = await axios.post(`${API_BASE_URL}/api/command`, commandData, {
        timeout: 1000 // Timeout court pour rapidité
      });

      // Pas de message pour les changements de fréquence (trop fréquents)
      if (!commandData.frequency) {
        showMessage('success', response.data.message);
      }

      // Pas de getStatus() automatique pour éviter les conflits
    } catch (error) {
      console.error('Erreur commande:', error);
      if (!commandData.frequency) { // Afficher erreur seulement pour les autres commandes
        showMessage('error', error.response?.data?.detail || 'Erreur de communication');
      }
    }
  };

  const getStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/status`);
      setRadioStatus(response.data);
      setIsConnected(true);
    } catch (error) {
      console.error('Erreur lecture état:', error);
      setIsConnected(false);
    }
  };

  // Gestionnaires d'événements pour les contrôles de fréquence - optimisé
  const handleFrequencyChange = (delta) => {
    const currentFreq = radioStatus.frequency || frequency;
    const newFreq = currentFreq + delta;
    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Mise à jour immédiate de l'affichage
      setFrequency(newFreq);
      setRadioStatus(prev => ({ ...prev, frequency: newFreq }));

      // Envoi immédiat de la commande
      sendCommand({ frequency: newFreq });
    }
  };

  const handleDirectFrequencySet = (freq) => {
    setFrequency(freq);
    sendCommand({ frequency: freq });
    // Mettre à jour les fréquences cibles
    setTargetFrequencies(prev =>
      prev.map(target => ({ ...target, active: target.freq === freq }))
    );
  };

  // Gestionnaires pour les contrôles audio
  const handleAfGainChange = (gain) => {
    setAfGain(gain);
    sendCommand({ af_gain: gain });
  };

  const handleRfGainChange = (gain) => {
    setRfGain(gain);
    sendCommand({ rf_gain: gain });
  };

  const handleModeChange = (newMode) => {
    setMode(newMode);
    sendCommand({ mode: newMode });
  };

  const handleFilterChange = (filter) => {
    setFilterBW(filter);
    const bwValue = parseFloat(filter.replace('K', '')) * 1000;
    sendCommand({ filter_width: bwValue });
  };

  // Gestionnaires pour le scan
  const startScan = async () => {
    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/start`, {
        start_frequency: scanStartFreq,
        end_frequency: scanEndFreq,
        step: scanStep,
        mode: mode
      });
      setIsScanning(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur scan');
    } finally {
      setLoading(false);
    }
  };

  const stopScan = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/stop`);
      setIsScanning(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt scan');
    }
  };

  // Gestionnaires pour l'audio streaming réel via WebSocket
  const startAudioStream = async () => {
    try {
      // Initialiser le contexte audio
      const context = new (window.AudioContext || window.webkitAudioContext)();
      audioContextRef.current = context;

      // Créer les nœuds audio
      const analyser = context.createAnalyser();
      analyser.fftSize = 256;
      analyserRef.current = analyser;

      const gainNode = context.createGain();
      gainNode.gain.value = volume / 100;
      gainNodeRef.current = gainNode;

      // Connecter à la sortie
      gainNode.connect(context.destination);
      analyser.connect(gainNode);

      // Établir la connexion WebSocket pour l'audio
      const ws = new WebSocket('ws://localhost:8001/ws/audio');
      websocketRef.current = ws;

      ws.onopen = () => {
        console.log('🎵 WebSocket audio connecté');
        showMessage('success', 'Connexion audio établie');
        // Demander le démarrage du streaming
        ws.send(JSON.stringify({
          type: 'start_stream'
        }));
      };

      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);

        if (message.type === 'stream_started') {
          setIsStreaming(true);
          showMessage('success', 'Streaming audio du récepteur démarré');
          startAudioAnalysis();
        } else if (message.type === 'audio_data') {
          // Décoder et jouer les données audio du récepteur
          playAudioData(message.data);
        } else if (message.type === 'stream_stopped') {
          setIsStreaming(false);
          showMessage('info', 'Streaming audio arrêté');
        }
      };

      ws.onclose = () => {
        console.log('🔇 WebSocket audio fermé');
        setIsStreaming(false);
      };

      ws.onerror = (error) => {
        console.error('❌ Erreur WebSocket audio:', error);
        showMessage('error', 'Erreur connexion audio - Vérifiez que le récepteur est connecté à la ligne d\'entrée');
        setIsStreaming(false);
      };

    } catch (error) {
      console.error('❌ Erreur démarrage streaming:', error);
      showMessage('error', 'Erreur démarrage streaming audio');
    }
  };

  // Fonction pour jouer les données audio reçues du récepteur
  const playAudioData = async (audioDataB64) => {
    try {
      if (!audioContextRef.current || !analyserRef.current || !gainNodeRef.current) return;

      // Décoder les données audio base64
      const audioBytes = atob(audioDataB64);
      const audioArray = new Int16Array(audioBytes.length / 2);

      for (let i = 0; i < audioArray.length; i++) {
        audioArray[i] = (audioBytes.charCodeAt(i * 2) & 0xFF) |
                       ((audioBytes.charCodeAt(i * 2 + 1) & 0xFF) << 8);
      }

      // Convertir en Float32Array pour Web Audio API
      const floatArray = new Float32Array(audioArray.length);
      for (let i = 0; i < audioArray.length; i++) {
        floatArray[i] = audioArray[i] / 32768.0;
      }

      // Créer un buffer audio
      const audioBuffer = audioContextRef.current.createBuffer(1, floatArray.length, 48000);
      audioBuffer.getChannelData(0).set(floatArray);

      // Créer une source et la connecter à la chaîne audio
      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;

      // Connecter: source -> analyser -> gain -> destination (haut-parleurs)
      source.connect(analyserRef.current);

      // Ajuster le volume selon le contrôle
      gainNodeRef.current.gain.value = (volume / 100) * (isMuted ? 0 : 1);

      // Jouer le son du récepteur sur les haut-parleurs du PC
      source.start();

    } catch (error) {
      console.error('❌ Erreur lecture audio du récepteur:', error);
    }
  };

  const stopAudioStream = () => {
    // Arrêter le WebSocket
    if (websocketRef.current) {
      websocketRef.current.send(JSON.stringify({
        type: 'stop_stream'
      }));
      websocketRef.current.close();
      websocketRef.current = null;
    }

    // Fermer le contexte audio
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setIsStreaming(false);
    showMessage('success', 'Streaming audio arrêté');
  };

  const startAudioAnalysis = () => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const analyze = () => {
      analyser.getByteFrequencyData(dataArray);
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      setAudioLevel(Math.round((average / 255) * 100));

      if (isStreaming) {
        requestAnimationFrame(analyze);
      }
    };

    analyze();
  };

  // Gestionnaires pour l'enregistrement
  const startRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: 'AF'
      });
      setIsRecording(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur enregistrement');
    }
  };

  const stopRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsRecording(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt enregistrement');
    }
  };

  // Gestionnaires pour les fréquences cibles
  const addTargetFrequency = () => {
    if (newFreqInput) {
      const freq = parseInt(newFreqInput);
      if (freq >= 100000 && freq <= 3000000000) {
        setTargetFrequencies(prev => [...prev, {
          freq: freq,
          name: `F${prev.length + 1}`,
          comment: newFreqComment || 'Nouvelle fréquence',
          active: false
        }]);
        setNewFreqInput('');
        setNewFreqComment('');
        showMessage('success', 'Fréquence cible ajoutée');
      }
    }
  };

  const removeTargetFrequency = (index) => {
    setTargetFrequencies(prev => prev.filter((_, i) => i !== index));
    showMessage('success', 'Fréquence cible supprimée');
  };

  // Gestionnaires pour l'alimentation
  const handlePowerOn = () => {
    sendCommand({ power_on: true });
  };

  const handlePowerOff = () => {
    sendCommand({ power_on: false });
  };

  // Gestionnaire pour la molette de la souris - corrigé
  useEffect(() => {
    const handleWheel = (e) => {
      // Vérifier si la souris est sur la molette rotative
      const knobElement = document.querySelector('.rotary-knob');
      if (knobElement && knobElement.contains(e.target)) {
        e.preventDefault();
        e.stopPropagation();
        const delta = e.deltaY > 0 ? -1 : 1;
        handleKnobRotation(delta);
        return false;
      }
    };

    // Ajouter l'événement avec { passive: false } pour permettre preventDefault
    document.addEventListener('wheel', handleWheel, { passive: false, capture: true });

    return () => {
      document.removeEventListener('wheel', handleWheel, { capture: true });
    };
  }, [handleKnobRotation]);

  // Effet pour charger les données au démarrage
  useEffect(() => {
    getStatus();
    // Intervalle plus court pour un affichage plus réactif
    const interval = setInterval(getStatus, 1000);

    // Nettoyage lors du démontage
    return () => {
      clearInterval(interval);
      if (websocketRef.current) {
        websocketRef.current.close();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Styles pour les boutons
  const buttonStyle = {
    base: {
      padding: '8px 16px',
      border: 'none',
      borderRadius: '6px',
      fontSize: '0.9rem',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      justifyContent: 'center',
      boxShadow: c2ewTheme.shadows.button,
      textTransform: 'uppercase',
      letterSpacing: '0.5px'
    },
    primary: {
      background: c2ewTheme.colors.accent,
      color: 'white'
    },
    success: {
      background: c2ewTheme.colors.success,
      color: 'white'
    },
    danger: {
      background: c2ewTheme.colors.danger,
      color: 'white'
    },
    warning: {
      background: c2ewTheme.colors.warning,
      color: 'white'
    },
    secondary: {
      background: c2ewTheme.colors.darkBorder,
      color: c2ewTheme.colors.text
    },
    active: {
      background: c2ewTheme.colors.success,
      color: 'white',
      boxShadow: `0 0 10px ${c2ewTheme.colors.success}`
    }
  };

  return (
    <div style={styles.container}>
      {/* Messages d'alerte compacts */}
      {message.text && (
        <div style={{
          padding: '8px 12px',
          borderRadius: '4px',
          marginBottom: '10px',
          fontSize: '0.8rem',
          backgroundColor: message.type === 'success' ?
            'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
          borderLeft: `3px solid ${message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger}`,
          color: message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger
        }}>
          {message.text}
        </div>
      )}

      {/* Grille principale compacte */}
      <div style={styles.mainGrid}>

        {/* Écran LCD et contrôles de fréquence */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Monitor size={16} />
            LCD - Fréquence
          </h2>

          {/* Écran LCD éditable */}
          <div style={styles.lcdDisplay}>
            {isEditingFreq ? (
              <div>
                <input
                  type="text"
                  value={frequencyInput}
                  onChange={(e) => setFrequencyInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') handleFrequencySubmit();
                    if (e.key === 'Escape') handleFrequencyCancel();
                  }}
                  style={styles.frequencyInput}
                  autoFocus
                  placeholder="000.000.000"
                />
                <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                  <button
                    onClick={handleFrequencySubmit}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.success,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✓ VALIDER
                  </button>
                  <button
                    onClick={handleFrequencyCancel}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.danger,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✗ ANNULER
                  </button>
                </div>
              </div>
            ) : (
              <div onClick={handleFrequencyEdit} style={{ cursor: 'pointer' }}>
                <div style={styles.lcdFrequency}>
                  {formatFrequencyLCD(radioStatus.frequency || frequency)}
                </div>
                <div style={styles.lcdInfo}>
                  <span>{radioStatus.mode || mode}</span>
                  <span>RSSI: {radioStatus.rssi || -80} dBm</span>
                  <span style={{
                    color: radioStatus.power_on ? c2ewTheme.colors.success : c2ewTheme.colors.danger,
                    fontWeight: 'bold'
                  }}>
                    {radioStatus.power_on ? 'ON' : 'OFF'}
                  </span>
                </div>
                <div style={{
                  fontSize: '0.7rem',
                  color: c2ewTheme.colors.textMuted,
                  marginTop: '5px'
                }}>
                  Cliquez pour éditer la fréquence
                </div>
              </div>
            )}
          </div>

          {/* Molette rotative et contrôles gains */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr auto 1fr',
            gap: '15px',
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            {/* AF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  AF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {afGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={afGain}
                onChange={(e) => handleAfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Molette centrale */}
            <div style={{ textAlign: 'center' }}>
              <div style={{
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '8px'
              }}>
                MOLETTE FRÉQ
              </div>
              <div
                className="rotary-knob"
                style={{
                  ...styles.rotaryKnob,
                  transform: `rotate(${knobRotation}deg)`
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  const rect = e.currentTarget.getBoundingClientRect();
                  const centerX = rect.left + rect.width / 2;
                  const clickX = e.clientX;
                  const delta = clickX > centerX ? 1 : -1;
                  handleKnobRotation(delta);
                }}
              >
                <div style={styles.rotaryIndicator}></div>
              </div>
              <div style={{
                fontSize: '0.7rem',
                color: c2ewTheme.colors.textMuted
              }}>
                ±25kHz
              </div>
            </div>

            {/* RF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  RF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {rfGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={rfGain}
                onChange={(e) => handleRfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>
          </div>

          {/* Contrôles rapides de fréquence */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '4px',
              marginBottom: '8px'
            }}>
              <button
                onClick={() => handleFrequencyChange(-100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -100k
              </button>
              <button
                onClick={() => handleFrequencyChange(-25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -25k
              </button>
              <button
                onClick={() => handleFrequencyChange(25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +25k
              </button>
              <button
                onClick={() => handleFrequencyChange(100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +100k
              </button>
            </div>
          </div>

          {/* Contrôles d'alimentation */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '8px'
          }}>
            <button
              onClick={handlePowerOn}
              disabled={loading || radioStatus.power_on}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.success,
                opacity: radioStatus.power_on ? 0.5 : 1,
                padding: '6px 8px',
                fontSize: '0.8rem'
              }}
            >
              <Power size={14} />
              ON
            </button>
            <button
              onClick={handlePowerOff}
              disabled={loading || !radioStatus.power_on}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.danger,
                opacity: !radioStatus.power_on ? 0.5 : 1,
                padding: '6px 8px',
                fontSize: '0.8rem'
              }}
            >
              <Power size={14} />
              OFF
            </button>
          </div>
        </div>

        {/* Contrôles Audio et Filtres */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Volume2 size={16} />
            Audio & Filtres
          </h2>

          {/* Modulation */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              MODULATION
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '4px'
            }}>
              {modes.slice(0, 8).map(m => (
                <button
                  key={m}
                  onClick={() => handleModeChange(m)}
                  style={{
                    ...buttonStyle.base,
                    ...(mode === m ? buttonStyle.active : buttonStyle.secondary),
                    padding: '4px 6px',
                    fontSize: '0.7rem'
                  }}
                  disabled={loading}
                >
                  {m}
                </button>
              ))}
            </div>
          </div>

          {/* Filtres BW */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              FILTRES BW
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '4px'
            }}>
              {bandwidthFilters.slice(0, 3).map(filter => (
                <button
                  key={filter}
                  onClick={() => handleFilterChange(filter)}
                  style={{
                    ...buttonStyle.base,
                    ...(filterBW === filter ? buttonStyle.active : buttonStyle.secondary),
                    padding: '4px 6px',
                    fontSize: '0.7rem'
                  }}
                  disabled={loading}
                >
                  {filter}
                </button>
              ))}
            </div>
          </div>

          {/* Audio Live et Enregistrement */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              AUDIO LIVE RÉCEPTEUR
            </div>

            {/* Contrôle de volume pour l'audio streaming */}
            <div style={{ marginBottom: '10px' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.7rem' }}>
                  VOLUME PC
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.7rem' }}>
                  {volume}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => {
                  const newVolume = parseInt(e.target.value);
                  setVolume(newVolume);
                  if (gainNodeRef.current) {
                    gainNodeRef.current.gain.value = (newVolume / 100) * (isMuted ? 0 : 1);
                  }
                }}
                style={{
                  width: '100%',
                  height: '3px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr 1fr',
              gap: '4px',
              marginBottom: '10px'
            }}>
              <button
                onClick={isStreaming ? stopAudioStream : startAudioStream}
                style={{
                  ...buttonStyle.base,
                  ...(isStreaming ? buttonStyle.danger : buttonStyle.success),
                  padding: '4px 6px',
                  fontSize: '0.7rem'
                }}
              >
                {isStreaming ? <Square size={12} /> : <Play size={12} />}
                {isStreaming ? 'STOP' : 'LIVE'}
              </button>
              <button
                onClick={() => {
                  setIsMuted(!isMuted);
                  if (gainNodeRef.current) {
                    gainNodeRef.current.gain.value = (volume / 100) * (!isMuted ? 0 : 1);
                  }
                }}
                style={{
                  ...buttonStyle.base,
                  ...(isMuted ? buttonStyle.danger : buttonStyle.secondary),
                  padding: '4px 6px',
                  fontSize: '0.7rem'
                }}
              >
                {isMuted ? <VolumeX size={12} /> : <Volume2 size={12} />}
                {isMuted ? 'MUTE' : 'AUDIO'}
              </button>
              <button
                onClick={isRecording ? stopRecording : startRecording}
                style={{
                  ...buttonStyle.base,
                  ...(isRecording ? buttonStyle.danger : buttonStyle.warning),
                  padding: '4px 6px',
                  fontSize: '0.7rem'
                }}
              >
                {isRecording ? <Square size={12} /> : <Mic size={12} />}
                {isRecording ? 'STOP' : 'REC'}
              </button>
            </div>

            {/* Niveau audio compact */}
            {(isStreaming || isRecording) && (
              <div style={{ marginBottom: '8px' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '3px'
                }}>
                  <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.7rem' }}>
                    NIVEAU
                  </span>
                  <span style={{ color: c2ewTheme.colors.success, fontSize: '0.7rem' }}>
                    {audioLevel}%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '3px',
                  background: c2ewTheme.colors.darkBorder,
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${audioLevel}%`,
                    height: '100%',
                    background: `linear-gradient(90deg, ${c2ewTheme.colors.success} 0%, ${c2ewTheme.colors.warning} 70%, ${c2ewTheme.colors.danger} 100%)`,
                    transition: 'width 0.1s ease'
                  }} />
                </div>
              </div>
            )}

            {/* Indicateurs d'état */}
            <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
              {isStreaming && (
                <div style={{
                  padding: '2px 6px',
                  background: c2ewTheme.colors.success,
                  color: 'white',
                  borderRadius: '3px',
                  fontSize: '0.6rem',
                  fontWeight: 'bold'
                }}>
                  🔊 LIVE
                </div>
              )}
              {isRecording && (
                <div style={{
                  padding: '2px 6px',
                  background: c2ewTheme.colors.danger,
                  color: 'white',
                  borderRadius: '3px',
                  fontSize: '0.6rem',
                  fontWeight: 'bold'
                }}>
                  🔴 REC
                </div>
              )}
            </div>
          </div>
        </div>

      </div>

      {/* Deuxième ligne - Scan et Fréquences cibles */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '10px',
        marginTop: '10px',
        maxWidth: '1200px',
        margin: '10px auto 0'
      }}>

        {/* Scan compact */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Scan size={16} />
            Scan
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            gap: '6px',
            marginBottom: '10px'
          }}>
            <div>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.7rem',
                marginBottom: '3px'
              }}>
                Début (Hz)
              </label>
              <input
                type="number"
                value={scanStartFreq}
                onChange={(e) => setScanStartFreq(parseInt(e.target.value))}
                placeholder="144000000"
                style={{
                  width: '100%',
                  padding: '6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
                disabled={loading || isScanning}
              />
            </div>
            <div>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.7rem',
                marginBottom: '3px'
              }}>
                Fin (Hz)
              </label>
              <input
                type="number"
                value={scanEndFreq}
                onChange={(e) => setScanEndFreq(parseInt(e.target.value))}
                placeholder="146000000"
                style={{
                  width: '100%',
                  padding: '6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
                disabled={loading || isScanning}
              />
            </div>
            <div>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.7rem',
                marginBottom: '3px'
              }}>
                Pas (Hz)
              </label>
              <input
                type="number"
                value={scanStep}
                onChange={(e) => setScanStep(parseInt(e.target.value))}
                placeholder="25000"
                style={{
                  width: '100%',
                  padding: '6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
                disabled={loading || isScanning}
              />
            </div>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '4px'
          }}>
            <button
              onClick={startScan}
              disabled={loading || isScanning}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.success,
                opacity: isScanning ? 0.5 : 1,
                padding: '4px 6px',
                fontSize: '0.7rem'
              }}
            >
              <Play size={12} />
              START
            </button>
            <button
              onClick={stopScan}
              disabled={loading || !isScanning}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.danger,
                opacity: !isScanning ? 0.5 : 1,
                padding: '4px 6px',
                fontSize: '0.7rem'
              }}
            >
              <Square size={12} />
              STOP
            </button>
          </div>

          {isScanning && (
            <div style={{
              marginTop: '6px',
              padding: '4px',
              background: c2ewTheme.colors.warning,
              color: 'white',
              borderRadius: '3px',
              fontSize: '0.6rem',
              textAlign: 'center'
            }}>
              🔍 SCANNING...
            </div>
          )}
        </div>

        {/* Fréquences cibles compactes */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Target size={16} />
            Fréquences Cibles
          </h2>

          {/* Ajout de fréquence avec commentaire */}
          <div style={{
            marginBottom: '12px',
            padding: '12px',
            background: c2ewTheme.colors.dark,
            borderRadius: '6px',
            border: `1px solid ${c2ewTheme.colors.darkBorder}`
          }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              AJOUTER FRÉQUENCE
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '6px'
            }}>
              <input
                type="number"
                value={newFreqInput}
                onChange={(e) => setNewFreqInput(e.target.value)}
                placeholder="Fréquence (Hz) - ex: 145500000"
                style={{
                  padding: '8px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.darkCard,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
              />
              <input
                type="text"
                value={newFreqComment}
                onChange={(e) => setNewFreqComment(e.target.value)}
                placeholder="Commentaire - ex: Canal d'urgence"
                style={{
                  padding: '8px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.darkCard,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
              />
              <button
                onClick={addTargetFrequency}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.success,
                  padding: '8px 12px',
                  fontSize: '0.8rem'
                }}
              >
                <Plus size={14} />
                AJOUTER
              </button>
            </div>
          </div>

          {/* Liste des fréquences cibles avec commentaires */}
          <div style={{
            maxHeight: '200px',
            overflowY: 'auto',
            border: `1px solid ${c2ewTheme.colors.darkBorder}`,
            borderRadius: '6px',
            background: c2ewTheme.colors.dark
          }}>
            {targetFrequencies.length === 0 ? (
              <div style={{
                textAlign: 'center',
                color: c2ewTheme.colors.textMuted,
                padding: '15px',
                fontSize: '0.8rem'
              }}>
                Aucune fréquence configurée
              </div>
            ) : (
              targetFrequencies.map((target, index) => (
                <div
                  key={index}
                  style={{
                    padding: '10px 12px',
                    borderBottom: index < targetFrequencies.length - 1 ?
                      `1px solid ${c2ewTheme.colors.darkBorder}` : 'none',
                    background: target.active ?
                      c2ewTheme.colors.success + '20' : 'transparent',
                    cursor: 'pointer',
                    transition: 'background 0.2s ease'
                  }}
                  onClick={() => handleDirectFrequencySet(target.freq)}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <div style={{
                        fontSize: '0.8rem',
                        color: target.active ? c2ewTheme.colors.success : c2ewTheme.colors.text,
                        fontWeight: 'bold',
                        marginBottom: '3px'
                      }}>
                        {target.comment}
                      </div>
                      <div style={{
                        fontSize: '0.8rem',
                        color: c2ewTheme.colors.textMuted,
                        fontFamily: '"Roboto Mono", monospace'
                      }}>
                        {formatFrequency(target.freq)}
                      </div>
                    </div>

                    <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>
                      {target.active && (
                        <div style={{
                          width: '8px',
                          height: '8px',
                          borderRadius: '50%',
                          background: c2ewTheme.colors.success,
                          boxShadow: `0 0 6px ${c2ewTheme.colors.success}`
                        }}></div>
                      )}

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeTargetFrequency(index);
                        }}
                        style={{
                          ...buttonStyle.base,
                          ...buttonStyle.danger,
                          padding: '4px 6px',
                          fontSize: '0.7rem'
                        }}
                      >
                        <Trash2 size={12} />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>


      {/* Styles CSS intégrés */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }

          input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
          }

          input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-moz-range-thumb {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-webkit-slider-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }

          input[type="range"]::-moz-range-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }
        `
      }} />
    </div>
  );
};

export default C2EWRadioControl;

import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { 
  Power, 
  Radio, 
  Volume2, 
  Settings, 
  Play, 
  Square, 
  Download,
  Trash2,
  RefreshCw,
  Zap,
  Activity,
  Target,
  Scan,
  Mic,
  MicOff,
  Plus,
  Minus,
  RotateCcw,
  Filter,
  Waves,
  Monitor,
  Headphones,
  VolumeX,
  MapPin,
  Clock,
  Signal,
  Wifi,
  WifiOff
} from 'lucide-react';

// Configuration de l'API
const API_BASE_URL = 'http://localhost:8000';

// Thème C2-EW Platform
const c2ewTheme = {
  colors: {
    primary: '#1e3a8a',      // Bleu foncé principal
    secondary: '#1e40af',     // Bleu moyen
    accent: '#3b82f6',        // Bleu clair
    success: '#10b981',       // Vert
    warning: '#f59e0b',       // Orange
    danger: '#ef4444',        // Rouge
    dark: '#0f172a',          // Noir bleu
    darkCard: '#1e293b',      // Gris bleu foncé
    darkBorder: '#334155',    // Bordure gris bleu
    text: '#f8fafc',          // Texte blanc
    textMuted: '#94a3b8',     // Texte gris
    lcd: '#00ff00',           // Vert LCD
    lcdBg: '#001100'          // Fond LCD
  },
  shadows: {
    card: '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
    button: '0 2px 4px rgba(0, 0, 0, 0.2)',
    inset: 'inset 0 2px 4px rgba(0, 0, 0, 0.3)'
  }
};

// Styles CSS-in-JS avec thème C2-EW
const styles = {
  container: {
    minHeight: '100vh',
    background: `linear-gradient(135deg, ${c2ewTheme.colors.dark} 0%, ${c2ewTheme.colors.darkCard} 100%)`,
    color: c2ewTheme.colors.text,
    fontFamily: '"Roboto Mono", "Courier New", monospace',
    padding: '20px'
  },
  
  header: {
    background: c2ewTheme.colors.primary,
    borderRadius: '8px',
    padding: '15px 25px',
    marginBottom: '20px',
    boxShadow: c2ewTheme.shadows.card,
    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  
  headerTitle: {
    display: 'flex',
    alignItems: 'center',
    gap: '15px'
  },
  
  title: {
    fontSize: '1.8rem',
    fontWeight: 'bold',
    margin: 0,
    color: c2ewTheme.colors.text
  },
  
  subtitle: {
    fontSize: '0.9rem',
    color: c2ewTheme.colors.textMuted,
    margin: '5px 0 0 0'
  },
  
  statusBadges: {
    display: 'flex',
    gap: '10px',
    alignItems: 'center'
  },
  
  badge: {
    padding: '4px 12px',
    borderRadius: '20px',
    fontSize: '0.75rem',
    fontWeight: 'bold',
    textTransform: 'uppercase',
    display: 'flex',
    alignItems: 'center',
    gap: '5px'
  },
  
  badgeOperational: {
    background: c2ewTheme.colors.success,
    color: 'white'
  },
  
  badgeConnected: {
    background: c2ewTheme.colors.accent,
    color: 'white'
  },
  
  badgeDisconnected: {
    background: c2ewTheme.colors.danger,
    color: 'white'
  },
  
  mainGrid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    gap: '20px',
    maxWidth: '1600px',
    margin: '0 auto'
  },
  
  card: {
    background: c2ewTheme.colors.darkCard,
    borderRadius: '8px',
    padding: '20px',
    boxShadow: c2ewTheme.shadows.card,
    border: `1px solid ${c2ewTheme.colors.darkBorder}`
  },
  
  cardTitle: {
    fontSize: '1.1rem',
    fontWeight: 'bold',
    marginBottom: '15px',
    color: c2ewTheme.colors.text,
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },
  
  lcdDisplay: {
    background: c2ewTheme.colors.lcdBg,
    border: `3px solid ${c2ewTheme.colors.darkBorder}`,
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center',
    marginBottom: '20px',
    boxShadow: c2ewTheme.shadows.inset,
    position: 'relative'
  },
  
  lcdFrequency: {
    fontSize: '2.5rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    color: c2ewTheme.colors.lcd,
    textShadow: `0 0 10px ${c2ewTheme.colors.lcd}`,
    marginBottom: '10px',
    letterSpacing: '2px'
  },
  
  lcdInfo: {
    fontSize: '0.9rem',
    color: c2ewTheme.colors.lcd,
    opacity: 0.8,
    display: 'flex',
    justifyContent: 'space-between'
  }
};

const C2EWRadioControl = () => {
  // États pour les contrôles
  const [frequency, setFrequency] = useState(145500000);
  const [mode, setMode] = useState('FM');
  const [afGain, setAfGain] = useState(50);
  const [rfGain, setRfGain] = useState(50);
  const [filterBW, setFilterBW] = useState('2.40K');
  const [squelch, setSquelch] = useState(0);
  
  // États pour le scan
  const [scanStartFreq, setScanStartFreq] = useState(144000000);
  const [scanEndFreq, setScanEndFreq] = useState(146000000);
  const [scanStep, setScanStep] = useState(25000);
  const [isScanning, setIsScanning] = useState(false);
  
  // États pour l'état du récepteur
  const [radioStatus, setRadioStatus] = useState({
    frequency: 145500000,
    mode: 'FM',
    rssi: -80,
    power_on: false,
    rf_gain: 50,
    af_gain: 50,
    filter_width: 2400,
    squelch: 0,
    volume: 50
  });
  
  // États pour l'enregistrement et audio
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  
  // États pour les fréquences cibles
  const [targetFrequencies, setTargetFrequencies] = useState([
    { freq: 145500000, name: 'R1 - Urgence', active: false },
    { freq: 145750000, name: 'R2 - Trafic', active: false },
    { freq: 146000000, name: 'R3 - Coordination', active: false },
    { freq: 433500000, name: 'UHF - Tactique', active: false }
  ]);
  const [newFreqInput, setNewFreqInput] = useState('');
  const [newFreqName, setNewFreqName] = useState('');
  
  // États pour les messages et connexion
  const [message, setMessage] = useState({ type: '', text: '' });
  const [loading, setLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState('EXCELLENT');
  
  // Références pour l'audio
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const gainNodeRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  
  // Modes de modulation disponibles
  const modes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM', 'NFM'];
  
  // Filtres de bande passante
  const bandwidthFilters = ['1.80K', '2.40K', '3.00K', '6.00K', '15.0K'];

  // Fonctions utilitaires
  const formatFrequency = (freq) => {
    if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  };

  const formatFrequencyLCD = (freq) => {
    const freqStr = freq.toString().padStart(9, '0');
    return `${freqStr.slice(0, 3)}.${freqStr.slice(3, 6)}.${freqStr.slice(6, 9)}`;
  };

  // Fonction pour afficher un message
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  // Fonctions API
  const sendCommand = async (commandData) => {
    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/command`, commandData);
      showMessage('success', response.data.message);
      await getStatus();
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur de communication');
    } finally {
      setLoading(false);
    }
  };

  const getStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/status`);
      setRadioStatus(response.data);
      setIsConnected(true);
    } catch (error) {
      console.error('Erreur lecture état:', error);
      setIsConnected(false);
    }
  };

  // Gestionnaires d'événements pour les contrôles de fréquence
  const handleFrequencyChange = (delta) => {
    const newFreq = frequency + delta;
    if (newFreq >= 100000 && newFreq <= 3000000000) {
      setFrequency(newFreq);
      sendCommand({ frequency: newFreq });
    }
  };

  const handleDirectFrequencySet = (freq) => {
    setFrequency(freq);
    sendCommand({ frequency: freq });
    // Mettre à jour les fréquences cibles
    setTargetFrequencies(prev =>
      prev.map(target => ({ ...target, active: target.freq === freq }))
    );
  };

  // Gestionnaires pour les contrôles audio
  const handleAfGainChange = (gain) => {
    setAfGain(gain);
    sendCommand({ af_gain: gain });
  };

  const handleRfGainChange = (gain) => {
    setRfGain(gain);
    sendCommand({ rf_gain: gain });
  };

  const handleModeChange = (newMode) => {
    setMode(newMode);
    sendCommand({ mode: newMode });
  };

  const handleFilterChange = (filter) => {
    setFilterBW(filter);
    const bwValue = parseFloat(filter.replace('K', '')) * 1000;
    sendCommand({ filter_width: bwValue });
  };

  // Gestionnaires pour le scan
  const startScan = async () => {
    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/start`, {
        start_frequency: scanStartFreq,
        end_frequency: scanEndFreq,
        step: scanStep,
        mode: mode
      });
      setIsScanning(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur scan');
    } finally {
      setLoading(false);
    }
  };

  const stopScan = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/stop`);
      setIsScanning(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt scan');
    }
  };

  // Gestionnaires pour l'audio streaming
  const startAudioStream = async () => {
    try {
      // Initialiser le contexte audio pour capturer l'audio du récepteur
      const context = new (window.AudioContext || window.webkitAudioContext)();
      audioContextRef.current = context;

      // Créer les nœuds audio
      const analyser = context.createAnalyser();
      analyser.fftSize = 256;
      analyserRef.current = analyser;

      const gainNode = context.createGain();
      gainNode.gain.value = volume / 100;
      gainNodeRef.current = gainNode;

      // Connecter à la sortie
      gainNode.connect(context.destination);
      analyser.connect(gainNode);

      setIsStreaming(true);
      showMessage('success', 'Streaming audio démarré');

      // Démarrer l'analyse du niveau audio
      startAudioAnalysis();
    } catch (error) {
      showMessage('error', 'Erreur démarrage streaming audio');
    }
  };

  const stopAudioStream = () => {
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    setIsStreaming(false);
    showMessage('success', 'Streaming audio arrêté');
  };

  const startAudioAnalysis = () => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const analyze = () => {
      analyser.getByteFrequencyData(dataArray);
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      setAudioLevel(Math.round((average / 255) * 100));

      if (isStreaming) {
        requestAnimationFrame(analyze);
      }
    };

    analyze();
  };

  // Gestionnaires pour l'enregistrement
  const startRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: 'AF'
      });
      setIsRecording(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur enregistrement');
    }
  };

  const stopRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsRecording(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt enregistrement');
    }
  };

  // Gestionnaires pour les fréquences cibles
  const addTargetFrequency = () => {
    if (newFreqInput && newFreqName) {
      const freq = parseInt(newFreqInput);
      if (freq >= 100000 && freq <= 3000000000) {
        setTargetFrequencies(prev => [...prev, {
          freq: freq,
          name: newFreqName,
          active: false
        }]);
        setNewFreqInput('');
        setNewFreqName('');
        showMessage('success', 'Fréquence cible ajoutée');
      }
    }
  };

  const removeTargetFrequency = (index) => {
    setTargetFrequencies(prev => prev.filter((_, i) => i !== index));
    showMessage('success', 'Fréquence cible supprimée');
  };

  // Gestionnaires pour l'alimentation
  const handlePowerOn = () => {
    sendCommand({ power_on: true });
  };

  const handlePowerOff = () => {
    sendCommand({ power_on: false });
  };

  // Effet pour charger les données au démarrage
  useEffect(() => {
    getStatus();
    const interval = setInterval(getStatus, 2000);
    return () => clearInterval(interval);
  }, []);

  // Styles pour les boutons
  const buttonStyle = {
    base: {
      padding: '8px 16px',
      border: 'none',
      borderRadius: '6px',
      fontSize: '0.9rem',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      justifyContent: 'center',
      boxShadow: c2ewTheme.shadows.button,
      textTransform: 'uppercase',
      letterSpacing: '0.5px'
    },
    primary: {
      background: c2ewTheme.colors.accent,
      color: 'white'
    },
    success: {
      background: c2ewTheme.colors.success,
      color: 'white'
    },
    danger: {
      background: c2ewTheme.colors.danger,
      color: 'white'
    },
    warning: {
      background: c2ewTheme.colors.warning,
      color: 'white'
    },
    secondary: {
      background: c2ewTheme.colors.darkBorder,
      color: c2ewTheme.colors.text
    },
    active: {
      background: c2ewTheme.colors.success,
      color: 'white',
      boxShadow: `0 0 10px ${c2ewTheme.colors.success}`
    }
  };

  return (
    <div style={styles.container}>
      {/* En-tête C2-EW Platform */}
      <div style={styles.header}>
        <div style={styles.headerTitle}>
          <Radio size={32} color={c2ewTheme.colors.text} />
          <div>
            <h1 style={styles.title}>C2-EW Platform</h1>
            <p style={styles.subtitle}>v1.0 - ICOM IC-R8600 Controller</p>
          </div>
        </div>

        <div style={styles.statusBadges}>
          <div style={{...styles.badge, ...styles.badgeOperational}}>
            <Signal size={14} />
            OPERATIONAL
          </div>
          <div style={{...styles.badge, ...(isConnected ? styles.badgeConnected : styles.badgeDisconnected)}}>
            {isConnected ? <Wifi size={14} /> : <WifiOff size={14} />}
            {isConnected ? 'CONNECTÉ' : 'DÉCONNECTÉ'}
          </div>
          <div style={{...styles.badge, ...styles.badgeConnected}}>
            <Clock size={14} />
            {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Messages d'alerte */}
      {message.text && (
        <div style={{
          padding: '15px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid',
          backgroundColor: message.type === 'success' ?
            'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
          borderColor: message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger,
          color: message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger
        }}>
          {message.text}
        </div>
      )}

      {/* Grille principale */}
      <div style={styles.mainGrid}>

        {/* Écran LCD et contrôles de fréquence */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Monitor size={20} />
            Écran LCD - Fréquence
          </h2>

          {/* Écran LCD */}
          <div style={styles.lcdDisplay}>
            <div style={styles.lcdFrequency}>
              {formatFrequencyLCD(radioStatus.frequency || frequency)}
            </div>
            <div style={styles.lcdInfo}>
              <span>{radioStatus.mode || mode}</span>
              <span>RSSI: {radioStatus.rssi || -80} dBm</span>
              <span>{radioStatus.power_on ? 'ON' : 'OFF'}</span>
            </div>
          </div>

          {/* Mécanisme de changement de fréquence */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(5, 1fr)',
              gap: '8px',
              marginBottom: '15px'
            }}>
              <button
                onClick={() => handleFrequencyChange(-1000000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Minus size={16} />
                1MHz
              </button>
              <button
                onClick={() => handleFrequencyChange(-100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Minus size={16} />
                100kHz
              </button>
              <button
                onClick={() => handleFrequencyChange(-25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Minus size={16} />
                25kHz
              </button>
              <button
                onClick={() => handleFrequencyChange(25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Plus size={16} />
                25kHz
              </button>
              <button
                onClick={() => handleFrequencyChange(100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Plus size={16} />
                100kHz
              </button>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '8px'
            }}>
              <button
                onClick={() => handleFrequencyChange(-10000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Minus size={16} />
                10kHz
              </button>
              <button
                onClick={() => handleFrequencyChange(-1000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Minus size={16} />
                1kHz
              </button>
              <button
                onClick={() => handleFrequencyChange(1000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary}}
                disabled={loading}
              >
                <Plus size={16} />
                1kHz
              </button>
            </div>
          </div>

          {/* Contrôles d'alimentation */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '10px'
          }}>
            <button
              onClick={handlePowerOn}
              disabled={loading || radioStatus.power_on}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.success,
                opacity: radioStatus.power_on ? 0.5 : 1
              }}
            >
              <Power size={16} />
              POWER ON
            </button>
            <button
              onClick={handlePowerOff}
              disabled={loading || !radioStatus.power_on}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.danger,
                opacity: !radioStatus.power_on ? 0.5 : 1
              }}
            >
              <Power size={16} />
              POWER OFF
            </button>
          </div>
        </div>

        {/* Contrôles Audio et Filtres */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Volume2 size={20} />
            Contrôles Audio
          </h2>

          {/* AF Gain */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '8px'
            }}>
              <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.9rem' }}>
                AF GAIN
              </span>
              <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold' }}>
                {afGain}%
              </span>
            </div>
            <input
              type="range"
              min="0"
              max="100"
              value={afGain}
              onChange={(e) => handleAfGainChange(parseInt(e.target.value))}
              style={{
                width: '100%',
                height: '6px',
                borderRadius: '3px',
                background: c2ewTheme.colors.darkBorder,
                outline: 'none',
                cursor: 'pointer'
              }}
            />
          </div>

          {/* RF Gain */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '8px'
            }}>
              <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.9rem' }}>
                RF GAIN
              </span>
              <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold' }}>
                {rfGain}%
              </span>
            </div>
            <input
              type="range"
              min="0"
              max="100"
              value={rfGain}
              onChange={(e) => handleRfGainChange(parseInt(e.target.value))}
              style={{
                width: '100%',
                height: '6px',
                borderRadius: '3px',
                background: c2ewTheme.colors.darkBorder,
                outline: 'none',
                cursor: 'pointer'
              }}
            />
          </div>

          {/* Modulation */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px'
            }}>
              MODULATION
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '6px'
            }}>
              {modes.map(m => (
                <button
                  key={m}
                  onClick={() => handleModeChange(m)}
                  style={{
                    ...buttonStyle.base,
                    ...(mode === m ? buttonStyle.active : buttonStyle.secondary),
                    padding: '6px 8px',
                    fontSize: '0.8rem'
                  }}
                  disabled={loading}
                >
                  {m}
                </button>
              ))}
            </div>
          </div>

          {/* Filtres BW */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px'
            }}>
              FILTRES BW
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '6px'
            }}>
              {bandwidthFilters.map(filter => (
                <button
                  key={filter}
                  onClick={() => handleFilterChange(filter)}
                  style={{
                    ...buttonStyle.base,
                    ...(filterBW === filter ? buttonStyle.active : buttonStyle.secondary),
                    padding: '6px 8px',
                    fontSize: '0.8rem'
                  }}
                  disabled={loading}
                >
                  <Filter size={14} />
                  {filter}
                </button>
              ))}
            </div>
          </div>

          {/* Contrôles Audio Streaming */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px'
            }}>
              AUDIO LIVE
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '8px',
              marginBottom: '15px'
            }}>
              <button
                onClick={isStreaming ? stopAudioStream : startAudioStream}
                style={{
                  ...buttonStyle.base,
                  ...(isStreaming ? buttonStyle.danger : buttonStyle.success)
                }}
              >
                {isStreaming ? <Square size={16} /> : <Play size={16} />}
                {isStreaming ? 'STOP' : 'STREAM'}
              </button>
              <button
                onClick={() => setIsMuted(!isMuted)}
                style={{
                  ...buttonStyle.base,
                  ...(isMuted ? buttonStyle.danger : buttonStyle.secondary)
                }}
              >
                {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
                {isMuted ? 'MUTE' : 'AUDIO'}
              </button>
            </div>

            {/* Niveau audio */}
            {isStreaming && (
              <div style={{ marginBottom: '10px' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '5px'
                }}>
                  <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                    NIVEAU
                  </span>
                  <span style={{ color: c2ewTheme.colors.success, fontSize: '0.8rem' }}>
                    {audioLevel}%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '4px',
                  background: c2ewTheme.colors.darkBorder,
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${audioLevel}%`,
                    height: '100%',
                    background: `linear-gradient(90deg, ${c2ewTheme.colors.success} 0%, ${c2ewTheme.colors.warning} 70%, ${c2ewTheme.colors.danger} 100%)`,
                    transition: 'width 0.1s ease'
                  }} />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Scan et Enregistrement */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Scan size={20} />
            Scan & Enregistrement
          </h2>

          {/* Contrôles de scan */}
          <div style={{ marginBottom: '25px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '15px'
            }}>
              SCAN DE FRÉQUENCES
            </div>

            <div style={{ marginBottom: '10px' }}>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '5px'
              }}>
                Début (Hz)
              </label>
              <input
                type="number"
                value={scanStartFreq}
                onChange={(e) => setScanStartFreq(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.9rem'
                }}
                disabled={loading || isScanning}
              />
            </div>

            <div style={{ marginBottom: '10px' }}>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '5px'
              }}>
                Fin (Hz)
              </label>
              <input
                type="number"
                value={scanEndFreq}
                onChange={(e) => setScanEndFreq(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.9rem'
                }}
                disabled={loading || isScanning}
              />
            </div>

            <div style={{ marginBottom: '15px' }}>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '5px'
              }}>
                Pas (Hz)
              </label>
              <input
                type="number"
                value={scanStep}
                onChange={(e) => setScanStep(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.9rem'
                }}
                disabled={loading || isScanning}
              />
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '8px'
            }}>
              <button
                onClick={startScan}
                disabled={loading || isScanning}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.success,
                  opacity: isScanning ? 0.5 : 1
                }}
              >
                <Play size={16} />
                START SCAN
              </button>
              <button
                onClick={stopScan}
                disabled={loading || !isScanning}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.danger,
                  opacity: !isScanning ? 0.5 : 1
                }}
              >
                <Square size={16} />
                STOP SCAN
              </button>
            </div>

            {isScanning && (
              <div style={{
                marginTop: '10px',
                padding: '8px',
                background: c2ewTheme.colors.warning,
                color: 'white',
                borderRadius: '4px',
                fontSize: '0.8rem',
                textAlign: 'center'
              }}>
                🔍 SCAN EN COURS...
              </div>
            )}
          </div>

          {/* Contrôles d'enregistrement */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '15px'
            }}>
              ENREGISTREMENT AUDIO
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '8px'
            }}>
              <button
                onClick={startRecording}
                disabled={loading || isRecording}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.danger,
                  opacity: isRecording ? 0.5 : 1
                }}
              >
                <Mic size={16} />
                REC START
              </button>
              <button
                onClick={stopRecording}
                disabled={loading || !isRecording}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.secondary,
                  opacity: !isRecording ? 0.5 : 1
                }}
              >
                <Square size={16} />
                REC STOP
              </button>
            </div>

            {isRecording && (
              <div style={{
                marginTop: '10px',
                padding: '8px',
                background: c2ewTheme.colors.danger,
                color: 'white',
                borderRadius: '4px',
                fontSize: '0.8rem',
                textAlign: 'center',
                animation: 'pulse 1s infinite'
              }}>
                🔴 ENREGISTREMENT EN COURS...
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Deuxième ligne - Fréquences cibles et statut */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '20px',
        marginTop: '20px',
        maxWidth: '1600px',
        margin: '20px auto 0'
      }}>

        {/* Fréquences cibles */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Target size={20} />
            Fréquences Cibles
          </h2>

          {/* Ajout de nouvelle fréquence */}
          <div style={{
            marginBottom: '20px',
            padding: '15px',
            background: c2ewTheme.colors.dark,
            borderRadius: '6px',
            border: `1px solid ${c2ewTheme.colors.darkBorder}`
          }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px'
            }}>
              AJOUTER FRÉQUENCE CIBLE
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr auto',
              gap: '8px',
              alignItems: 'end'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.8rem',
                  marginBottom: '5px'
                }}>
                  Fréquence (Hz)
                </label>
                <input
                  type="number"
                  value={newFreqInput}
                  onChange={(e) => setNewFreqInput(e.target.value)}
                  placeholder="145500000"
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.9rem'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.8rem',
                  marginBottom: '5px'
                }}>
                  Nom
                </label>
                <input
                  type="text"
                  value={newFreqName}
                  onChange={(e) => setNewFreqName(e.target.value)}
                  placeholder="Nom de la fréquence"
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.9rem'
                  }}
                />
              </div>

              <button
                onClick={addTargetFrequency}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.success,
                  padding: '8px 12px'
                }}
              >
                <Plus size={16} />
                AJOUTER
              </button>
            </div>
          </div>

          {/* Liste des fréquences cibles */}
          <div style={{
            maxHeight: '300px',
            overflowY: 'auto',
            border: `1px solid ${c2ewTheme.colors.darkBorder}`,
            borderRadius: '6px',
            background: c2ewTheme.colors.dark
          }}>
            {targetFrequencies.length === 0 ? (
              <div style={{
                textAlign: 'center',
                color: c2ewTheme.colors.textMuted,
                padding: '20px'
              }}>
                Aucune fréquence cible configurée
              </div>
            ) : (
              targetFrequencies.map((target, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '12px 15px',
                    borderBottom: index < targetFrequencies.length - 1 ?
                      `1px solid ${c2ewTheme.colors.darkBorder}` : 'none',
                    background: target.active ?
                      `rgba(${parseInt(c2ewTheme.colors.success.slice(1, 3), 16)}, ${parseInt(c2ewTheme.colors.success.slice(3, 5), 16)}, ${parseInt(c2ewTheme.colors.success.slice(5, 7), 16)}, 0.1)` :
                      'transparent',
                    cursor: 'pointer',
                    transition: 'background 0.2s ease'
                  }}
                  onClick={() => handleDirectFrequencySet(target.freq)}
                >
                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontWeight: '600',
                      marginBottom: '3px',
                      color: target.active ? c2ewTheme.colors.success : c2ewTheme.colors.text
                    }}>
                      {target.name}
                    </div>
                    <div style={{
                      fontSize: '0.9rem',
                      color: c2ewTheme.colors.textMuted,
                      fontFamily: '"Roboto Mono", monospace'
                    }}>
                      {formatFrequency(target.freq)}
                    </div>
                  </div>

                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    {target.active && (
                      <div style={{
                        ...styles.badge,
                        ...styles.badgeOperational,
                        padding: '2px 8px',
                        fontSize: '0.7rem'
                      }}>
                        <Activity size={12} />
                        ACTIF
                      </div>
                    )}

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeTargetFrequency(index);
                      }}
                      style={{
                        ...buttonStyle.base,
                        ...buttonStyle.danger,
                        padding: '4px 8px',
                        fontSize: '0.7rem'
                      }}
                    >
                      <Trash2 size={12} />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Statut système */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Activity size={20} />
            Statut Système
          </h2>

          {/* Indicateurs de statut */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '12px'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 12px',
                background: c2ewTheme.colors.dark,
                borderRadius: '4px',
                border: `1px solid ${c2ewTheme.colors.darkBorder}`
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.9rem' }}>
                  Alimentation:
                </span>
                <div style={{
                  ...styles.badge,
                  ...(radioStatus.power_on ? styles.badgeOperational : styles.badgeDisconnected),
                  fontSize: '0.7rem'
                }}>
                  {radioStatus.power_on ? 'ON' : 'OFF'}
                </div>
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 12px',
                background: c2ewTheme.colors.dark,
                borderRadius: '4px',
                border: `1px solid ${c2ewTheme.colors.darkBorder}`
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.9rem' }}>
                  Mode:
                </span>
                <span style={{
                  color: c2ewTheme.colors.text,
                  fontWeight: 'bold',
                  fontFamily: '"Roboto Mono", monospace'
                }}>
                  {radioStatus.mode || mode}
                </span>
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 12px',
                background: c2ewTheme.colors.dark,
                borderRadius: '4px',
                border: `1px solid ${c2ewTheme.colors.darkBorder}`
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.9rem' }}>
                  RSSI:
                </span>
                <span style={{
                  color: c2ewTheme.colors.success,
                  fontWeight: 'bold',
                  fontFamily: '"Roboto Mono", monospace'
                }}>
                  {radioStatus.rssi || -80} dBm
                </span>
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 12px',
                background: c2ewTheme.colors.dark,
                borderRadius: '4px',
                border: `1px solid ${c2ewTheme.colors.darkBorder}`
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.9rem' }}>
                  Filtre BW:
                </span>
                <span style={{
                  color: c2ewTheme.colors.accent,
                  fontWeight: 'bold',
                  fontFamily: '"Roboto Mono", monospace'
                }}>
                  {filterBW}
                </span>
              </div>
            </div>
          </div>

          {/* Contrôles système */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px'
            }}>
              CONTRÔLES SYSTÈME
            </div>

            <button
              onClick={getStatus}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.primary,
                width: '100%',
                marginBottom: '8px'
              }}
            >
              <RefreshCw size={16} />
              ACTUALISER STATUT
            </button>

            <button
              onClick={() => {
                setFrequency(145500000);
                setMode('FM');
                setAfGain(50);
                setRfGain(50);
                setFilterBW('2.40K');
                showMessage('success', 'Paramètres réinitialisés');
              }}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.warning,
                width: '100%'
              }}
            >
              <RotateCcw size={16} />
              RESET PARAMÈTRES
            </button>
          </div>

          {/* Informations de connexion */}
          <div style={{
            padding: '12px',
            background: c2ewTheme.colors.dark,
            borderRadius: '6px',
            border: `1px solid ${c2ewTheme.colors.darkBorder}`
          }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              INFORMATIONS CONNEXION
            </div>

            <div style={{ fontSize: '0.8rem', lineHeight: '1.4' }}>
              <div style={{ marginBottom: '4px' }}>
                <span style={{ color: c2ewTheme.colors.textMuted }}>API: </span>
                <span style={{ color: isConnected ? c2ewTheme.colors.success : c2ewTheme.colors.danger }}>
                  {isConnected ? 'Connecté' : 'Déconnecté'}
                </span>
              </div>
              <div style={{ marginBottom: '4px' }}>
                <span style={{ color: c2ewTheme.colors.textMuted }}>Qualité: </span>
                <span style={{ color: c2ewTheme.colors.accent }}>
                  {connectionQuality}
                </span>
              </div>
              <div>
                <span style={{ color: c2ewTheme.colors.textMuted }}>Dernière MAJ: </span>
                <span style={{ color: c2ewTheme.colors.text }}>
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>

          {/* Raccourcis clavier */}
          <div style={{
            marginTop: '15px',
            padding: '12px',
            background: c2ewTheme.colors.dark,
            borderRadius: '6px',
            border: `1px solid ${c2ewTheme.colors.darkBorder}`
          }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              RACCOURCIS CLAVIER
            </div>

            <div style={{ fontSize: '0.7rem', lineHeight: '1.3', color: c2ewTheme.colors.textMuted }}>
              <div>↑↓: ±25kHz | ←→: ±1kHz</div>
              <div>Shift+↑↓: ±1MHz | Shift+←→: ±10kHz</div>
              <div>ESPACE: Power ON/OFF</div>
              <div>M: Mute/Unmute | R: Record</div>
            </div>
          </div>
        </div>
      </div>

      {/* CSS pour les animations */}
      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }

        input[type="range"] {
          -webkit-appearance: none;
          appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: ${c2ewTheme.colors.accent};
          cursor: pointer;
          border: 2px solid ${c2ewTheme.colors.text};
        }

        input[type="range"]::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: ${c2ewTheme.colors.accent};
          cursor: pointer;
          border: 2px solid ${c2ewTheme.colors.text};
        }

        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto+Mono:wght@300;400;500;700&display=swap');
      `}</style>
    </div>
  );
};

export default C2EWRadioControl;

import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import '../c2ew-styles.css';
import { 
  Power, 
  Radio, 
  Volume2, 
  Settings, 
  Play, 
  Square, 
  Download,
  Trash2,
  RefreshCw,
  Zap,
  Activity,
  Target,
  Scan,
  Mic,
  MicOff,
  Plus,
  Minus,
  RotateCcw,
  Filter,
  Waves,
  Monitor,
  Headphones,
  VolumeX,
  MapPin,
  Clock,
  Signal,
  Wifi,
  WifiOff
} from 'lucide-react';

// Configuration de l'API
const API_BASE_URL = 'http://localhost:8001';

// Thème C2-EW Platform - Version compacte et sombre
const c2ewTheme = {
  colors: {
    primary: '#000000',       // Noir pur
    secondary: '#111111',     // Noir léger
    accent: '#2563eb',        // Bleu accent
    success: '#10b981',       // Vert
    warning: '#f59e0b',       // Orange
    danger: '#ef4444',        // Rouge
    dark: '#000000',          // Noir pur
    darkCard: '#111111',      // Noir léger pour cartes
    darkBorder: '#333333',    // Bordure gris foncé
    text: '#ffffff',          // Texte blanc pur
    textMuted: '#888888',     // Texte gris
    lcd: '#00ff41',           // Vert LCD vif
    lcdBg: '#000000'          // Fond LCD noir
  },
  shadows: {
    card: '0 2px 8px rgba(0, 0, 0, 0.8)',
    button: '0 1px 3px rgba(0, 0, 0, 0.5)',
    inset: 'inset 0 1px 3px rgba(0, 0, 0, 0.8)'
  }
};

// Styles CSS-in-JS avec thème C2-EW - Version compacte
const styles = {
  container: {
    background: c2ewTheme.colors.dark,
    color: c2ewTheme.colors.text,
    fontFamily: '"Roboto Mono", "Courier New", monospace',
    padding: '10px',
    minHeight: '100vh'
  },

  mainGrid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '15px',
    maxWidth: '1400px',
    margin: '0 auto'
  },

  card: {
    background: c2ewTheme.colors.darkCard,
    borderRadius: '8px',
    padding: '20px',
    boxShadow: c2ewTheme.shadows.card,
    border: `1px solid ${c2ewTheme.colors.darkBorder}`
  },

  cardTitle: {
    fontSize: '0.9rem',
    fontWeight: 'bold',
    marginBottom: '10px',
    color: c2ewTheme.colors.text,
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },

  lcdDisplay: {
    background: c2ewTheme.colors.lcdBg,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    borderRadius: '6px',
    padding: '15px',
    textAlign: 'center',
    marginBottom: '15px',
    boxShadow: c2ewTheme.shadows.inset,
    position: 'relative'
  },

  lcdFrequency: {
    fontSize: '2rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    color: c2ewTheme.colors.lcd,
    textShadow: `0 0 8px ${c2ewTheme.colors.lcd}`,
    marginBottom: '8px',
    letterSpacing: '1px',
    cursor: 'pointer'
  },

  lcdInfo: {
    fontSize: '0.8rem',
    color: c2ewTheme.colors.lcd,
    opacity: 0.8,
    display: 'flex',
    justifyContent: 'space-between'
  },

  frequencyInput: {
    background: c2ewTheme.colors.lcdBg,
    border: `1px solid ${c2ewTheme.colors.lcd}`,
    borderRadius: '4px',
    padding: '8px',
    color: c2ewTheme.colors.lcd,
    fontSize: '1.5rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    textAlign: 'center',
    width: '100%',
    marginBottom: '10px'
  },

  rotaryKnob: {
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    background: `linear-gradient(145deg, ${c2ewTheme.colors.darkBorder}, ${c2ewTheme.colors.secondary})`,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    cursor: 'pointer',
    position: 'relative',
    margin: '10px auto',
    boxShadow: c2ewTheme.shadows.button
  },

  rotaryIndicator: {
    position: 'absolute',
    top: '5px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '3px',
    height: '20px',
    background: c2ewTheme.colors.accent,
    borderRadius: '2px'
  }
};

const C2EWRadioControl = () => {
  // États pour les contrôles
  const [frequency, setFrequency] = useState(145500000);
  const [mode, setMode] = useState('FM');
  const [afGain, setAfGain] = useState(50);
  const [rfGain, setRfGain] = useState(50);
  const [filterBW, setFilterBW] = useState('2.40K');
  const [squelch, setSquelch] = useState(0);

  // États pour l'édition de fréquence
  const [isEditingFreq, setIsEditingFreq] = useState(false);
  const [frequencyInput, setFrequencyInput] = useState('145.500.000');
  const [knobRotation, setKnobRotation] = useState(0);
  
  // États pour le scan
  const [scanStartFreq, setScanStartFreq] = useState(144000000);
  const [scanEndFreq, setScanEndFreq] = useState(146000000);
  const [scanStep, setScanStep] = useState(25000);
  const [isScanning, setIsScanning] = useState(false);
  
  // États pour l'état du récepteur
  const [radioStatus, setRadioStatus] = useState({
    frequency: 145500000,
    mode: 'FM',
    rssi: -80,
    power_on: false,
    rf_gain: 50,
    af_gain: 50,
    filter_width: 2400,
    squelch: 0,
    volume: 50
  });
  
  // États pour l'enregistrement et audio
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [commandInProgress, setCommandInProgress] = useState(false);
  const [userChangingFreq, setUserChangingFreq] = useState(false);
  
  // États pour les fréquences cibles
  const [targetFrequencies, setTargetFrequencies] = useState([
    { freq: 145500000, name: 'R1 - Urgence', comment: 'Canal d\'urgence principal', active: false },
    { freq: 145750000, name: 'R2 - Trafic', comment: 'Coordination trafic', active: false },
    { freq: 146000000, name: 'R3 - Coordination', comment: 'Canal de coordination', active: false },
    { freq: 433500000, name: 'UHF - Tactique', comment: 'Fréquence tactique UHF', active: false }
  ]);
  const [newFreqInput, setNewFreqInput] = useState('');
  const [newFreqComment, setNewFreqComment] = useState('');
  
  // États pour les messages et connexion
  const [message, setMessage] = useState({ type: '', text: '' });
  const [isConnected, setIsConnected] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState('EXCELLENT');
  
  // Références pour l'audio
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const gainNodeRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const websocketRef = useRef(null);
  const audioBufferRef = useRef([]);
  
  // Modes de modulation disponibles
  const modes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM', 'NFM'];
  
  // Filtres de bande passante
  const bandwidthFilters = ['1.80K', '2.40K', '3.00K', '6.00K', '15.0K'];

  // Fonctions utilitaires
  const formatFrequency = (freq) => {
    if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  };

  const formatFrequencyLCD = (freq) => {
    const freqStr = freq.toString().padStart(9, '0');
    return `${freqStr.slice(0, 3)}.${freqStr.slice(3, 6)}.${freqStr.slice(6, 9)}`;
  };

  const parseFrequencyInput = (input) => {
    return parseInt(input.replace(/\./g, ''));
  };

  // Gestionnaire pour l'édition directe de fréquence
  const handleFrequencyEdit = () => {
    setIsEditingFreq(true);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  const handleFrequencySubmit = () => {
    const newFreq = parseFrequencyInput(frequencyInput);
    if (newFreq >= 100000 && newFreq <= 3000000000) {
      setIsEditingFreq(false);

      // Envoi de la commande et synchronisation
      sendCommandAndUpdate({ frequency: newFreq });
      showMessage('success', `Fréquence changée: ${formatFrequencyLCD(newFreq)} Hz`);
    } else {
      showMessage('error', 'Fréquence invalide (100kHz - 3GHz)');
    }
  };

  const handleFrequencyCancel = () => {
    setIsEditingFreq(false);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  // Gestionnaire pour la molette rotative - 1kHz par cran, rapide et précis
  const handleKnobRotation = useCallback((delta) => {
    const step = 1000; // 1kHz par cran pour rapidité et précision
    const currentFreq = frequency; // Utiliser la fréquence locale, pas celle du récepteur
    const newFreq = currentFreq + (delta * step);

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      setKnobRotation(prev => prev + delta * 10); // 10 degrés par cran

      // Indiquer que l'utilisateur change la fréquence
      setUserChangingFreq(true);

      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);

      // Envoi immédiat au récepteur sans attendre
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 1000);

      console.log(`Molette: ${formatFrequencyLCD(newFreq)} Hz`);
    }
  }, [frequency]);

  // Fonction pour afficher un message
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  // Fonctions API - ultra-rapide pour molette 1kHz
  const sendCommand = async (commandData) => {
    try {
      // Envoi fire-and-forget pour la molette (pas d'attente)
      axios.post(`${API_BASE_URL}/api/command`, commandData, {
        timeout: 300 // Timeout ultra-court
      }).then(response => {
        // Succès silencieux pour la fréquence
        if (!commandData.frequency) {
          showMessage('success', 'Commande envoyée');
        }
      }).catch(error => {
        // Ignorer les erreurs de timeout pour la molette
        if (!commandData.frequency) {
          console.error('Erreur commande:', error);
          showMessage('error', 'Erreur de communication');
        }
      });

    } catch (error) {
      // Ignorer complètement les erreurs pour la molette
      if (!commandData.frequency) {
        console.error('Erreur commande:', error);
        showMessage('error', 'Erreur de communication');
      }
    }
  };

  // Nouvelle fonction qui envoie la commande ET récupère le statut réel
  const sendCommandAndUpdate = async (commandData) => {
    // Éviter les commandes multiples simultanées
    if (commandInProgress) {
      console.log('Commande en cours, ignorée');
      return;
    }

    setCommandInProgress(true);

    try {
      // Envoi de la commande avec timeout plus long
      await axios.post(`${API_BASE_URL}/api/command`, commandData, {
        timeout: 3000
      });

      // Attendre que le récepteur traite la commande
      setTimeout(async () => {
        try {
          // Récupérer le statut réel du récepteur
          await getStatus();
        } catch (statusError) {
          console.error('Erreur récupération statut:', statusError);
        } finally {
          setCommandInProgress(false);
        }
      }, 300);

    } catch (error) {
      console.error('Erreur commande:', error);
      setCommandInProgress(false);

      // Afficher erreur seulement pour les commandes importantes
      if (!commandData.frequency && !commandData.af_gain && !commandData.rf_gain) {
        showMessage('error', 'Erreur de communication avec le récepteur');
      }
    }
  };

  const getStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/status`);
      const newStatus = response.data;
      setRadioStatus(newStatus);
      setIsConnected(true);

      // NE PAS écraser la fréquence si l'utilisateur utilise la molette
      if (!userChangingFreq && newStatus.frequency && newStatus.frequency !== frequency) {
        setFrequency(newStatus.frequency);
        console.log(`Sync récepteur: ${formatFrequencyLCD(newStatus.frequency)} Hz`);
      }

      // Synchroniser les autres valeurs
      if (newStatus.af_gain !== undefined) setAfGain(newStatus.af_gain);
      if (newStatus.rf_gain !== undefined) setRfGain(newStatus.rf_gain);
      if (newStatus.mode) setMode(newStatus.mode);

    } catch (error) {
      console.error('Erreur lecture état:', error);
      setIsConnected(false);
    }
  };

  // Gestionnaires d'événements pour les contrôles de fréquence - synchronisé
  const handleFrequencyChange = (delta) => {
    const currentFreq = frequency; // Utiliser la fréquence locale
    const newFreq = currentFreq + delta;

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Indiquer que l'utilisateur change la fréquence
      setUserChangingFreq(true);

      // Mise à jour immédiate de l'affichage
      setFrequency(newFreq);

      // Envoi immédiat au récepteur
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 1000);

      console.log(`Bouton: ${formatFrequencyLCD(newFreq)} Hz (${delta > 0 ? '+' : ''}${delta/1000}kHz)`);
    }
  };

  const handleDirectFrequencySet = (freq) => {
    sendCommandAndUpdate({ frequency: freq });
    // Mettre à jour les fréquences cibles
    setTargetFrequencies(prev =>
      prev.map(target => ({ ...target, active: target.freq === freq }))
    );
  };

  // Gestionnaires pour les contrôles audio - avec debouncing
  const handleAfGainChange = useCallback((gain) => {
    setAfGain(gain);
    setRadioStatus(prev => ({ ...prev, af_gain: gain }));

    // Debouncing pour éviter trop de commandes
    clearTimeout(window.afGainTimeout);
    window.afGainTimeout = setTimeout(() => {
      if (!commandInProgress) {
        sendCommandAndUpdate({ af_gain: gain });
      }
    }, 200);
  }, [commandInProgress]);

  const handleRfGainChange = useCallback((gain) => {
    setRfGain(gain);
    setRadioStatus(prev => ({ ...prev, rf_gain: gain }));

    // Debouncing pour éviter trop de commandes
    clearTimeout(window.rfGainTimeout);
    window.rfGainTimeout = setTimeout(() => {
      if (!commandInProgress) {
        sendCommandAndUpdate({ rf_gain: gain });
      }
    }, 200);
  }, [commandInProgress]);

  // Gestionnaires pour modulation et filtres
  const handleModeChange = (newMode) => {
    setMode(newMode);
    sendCommand({ mode: newMode });
    console.log(`Mode changé: ${newMode}`);
  };

  const handleFilterChange = (newFilter) => {
    setFilterBW(newFilter);
    sendCommand({ filter_width: newFilter });
    console.log(`Filtre changé: ${newFilter}`);
  };

  const handleModeChange = (newMode) => {
    setMode(newMode);
    sendCommandAndUpdate({ mode: newMode });
  };

  const handleFilterChange = (filter) => {
    setFilterBW(filter);
    const bwValue = parseFloat(filter.replace('K', '')) * 1000;
    sendCommandAndUpdate({ filter_width: bwValue });
  };

  // Gestionnaires pour le scan
  const startScan = async () => {
    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/start`, {
        start_frequency: scanStartFreq,
        end_frequency: scanEndFreq,
        step: scanStep,
        mode: mode
      });
      setIsScanning(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur scan');
    } finally {
      setLoading(false);
    }
  };

  const stopScan = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/stop`);
      setIsScanning(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt scan');
    }
  };

  // Gestionnaires pour l'audio streaming réel via WebSocket
  const startAudioStream = async () => {
    try {
      // Initialiser le contexte audio
      const context = new (window.AudioContext || window.webkitAudioContext)();
      audioContextRef.current = context;

      // Créer les nœuds audio
      const analyser = context.createAnalyser();
      analyser.fftSize = 256;
      analyserRef.current = analyser;

      const gainNode = context.createGain();
      gainNode.gain.value = volume / 100;
      gainNodeRef.current = gainNode;

      // Connecter à la sortie
      gainNode.connect(context.destination);
      analyser.connect(gainNode);

      // Établir la connexion WebSocket pour l'audio
      const ws = new WebSocket('ws://localhost:8001/ws/audio');
      websocketRef.current = ws;

      ws.onopen = () => {
        console.log('🎵 WebSocket audio connecté');
        showMessage('success', 'Connexion audio établie');
        // Demander le démarrage du streaming
        ws.send(JSON.stringify({
          type: 'start_stream'
        }));
      };

      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);

        if (message.type === 'stream_started') {
          setIsStreaming(true);
          showMessage('success', 'Streaming audio du récepteur démarré');
          startAudioAnalysis();
        } else if (message.type === 'audio_data') {
          // Décoder et jouer les données audio du récepteur
          playAudioData(message.data);
        } else if (message.type === 'stream_stopped') {
          setIsStreaming(false);
          showMessage('info', 'Streaming audio arrêté');
        }
      };

      ws.onclose = () => {
        console.log('🔇 WebSocket audio fermé');
        setIsStreaming(false);
      };

      ws.onerror = (error) => {
        console.error('❌ Erreur WebSocket audio:', error);
        showMessage('error', 'Erreur connexion audio - Vérifiez que le récepteur est connecté à la ligne d\'entrée');
        setIsStreaming(false);
      };

    } catch (error) {
      console.error('❌ Erreur démarrage streaming:', error);
      showMessage('error', 'Erreur démarrage streaming audio');
    }
  };

  // Fonction pour jouer les données audio reçues du récepteur
  const playAudioData = async (audioDataB64) => {
    try {
      if (!audioContextRef.current || !analyserRef.current || !gainNodeRef.current) return;

      // Décoder les données audio base64
      const audioBytes = atob(audioDataB64);
      const audioArray = new Int16Array(audioBytes.length / 2);

      for (let i = 0; i < audioArray.length; i++) {
        audioArray[i] = (audioBytes.charCodeAt(i * 2) & 0xFF) |
                       ((audioBytes.charCodeAt(i * 2 + 1) & 0xFF) << 8);
      }

      // Convertir en Float32Array pour Web Audio API
      const floatArray = new Float32Array(audioArray.length);
      for (let i = 0; i < audioArray.length; i++) {
        floatArray[i] = audioArray[i] / 32768.0;
      }

      // Créer un buffer audio
      const audioBuffer = audioContextRef.current.createBuffer(1, floatArray.length, 48000);
      audioBuffer.getChannelData(0).set(floatArray);

      // Créer une source et la connecter à la chaîne audio
      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;

      // Connecter: source -> analyser -> gain -> destination (haut-parleurs)
      source.connect(analyserRef.current);

      // Ajuster le volume selon le contrôle
      gainNodeRef.current.gain.value = (volume / 100) * (isMuted ? 0 : 1);

      // Jouer le son du récepteur sur les haut-parleurs du PC
      source.start();

    } catch (error) {
      console.error('❌ Erreur lecture audio du récepteur:', error);
    }
  };

  const stopAudioStream = () => {
    // Arrêter le WebSocket
    if (websocketRef.current) {
      websocketRef.current.send(JSON.stringify({
        type: 'stop_stream'
      }));
      websocketRef.current.close();
      websocketRef.current = null;
    }

    // Fermer le contexte audio
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setIsStreaming(false);
    showMessage('success', 'Streaming audio arrêté');
  };

  const startAudioAnalysis = () => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const analyze = () => {
      analyser.getByteFrequencyData(dataArray);
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      setAudioLevel(Math.round((average / 255) * 100));

      if (isStreaming) {
        requestAnimationFrame(analyze);
      }
    };

    analyze();
  };

  // Gestionnaires pour l'enregistrement
  const startRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: 'AF'
      });
      setIsRecording(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur enregistrement');
    }
  };

  const stopRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsRecording(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt enregistrement');
    }
  };

  // Gestionnaires pour les fréquences cibles
  const addTargetFrequency = () => {
    if (newFreqInput) {
      const freq = parseInt(newFreqInput);
      if (freq >= 100000 && freq <= 3000000000) {
        setTargetFrequencies(prev => [...prev, {
          freq: freq,
          name: `F${prev.length + 1}`,
          comment: newFreqComment || 'Nouvelle fréquence',
          active: false
        }]);
        setNewFreqInput('');
        setNewFreqComment('');
        showMessage('success', 'Fréquence cible ajoutée');
      }
    }
  };

  const removeTargetFrequency = (index) => {
    setTargetFrequencies(prev => prev.filter((_, i) => i !== index));
    showMessage('success', 'Fréquence cible supprimée');
  };

  // Gestionnaires pour l'alimentation
  const handlePowerOn = () => {
    sendCommand({ power_on: true });
  };

  const handlePowerOff = () => {
    sendCommand({ power_on: false });
  };

  // Gestionnaire pour la molette de la souris - corrigé
  useEffect(() => {
    const handleWheel = (e) => {
      // Vérifier si la souris est sur la molette rotative
      const knobElement = document.querySelector('.rotary-knob');
      if (knobElement && knobElement.contains(e.target)) {
        e.preventDefault();
        e.stopPropagation();
        const delta = e.deltaY > 0 ? -1 : 1;
        handleKnobRotation(delta);
        return false;
      }
    };

    // Ajouter l'événement avec { passive: false } pour permettre preventDefault
    document.addEventListener('wheel', handleWheel, { passive: false, capture: true });

    return () => {
      document.removeEventListener('wheel', handleWheel, { capture: true });
    };
  }, [handleKnobRotation]);

  // Effet pour charger les données au démarrage
  useEffect(() => {
    getStatus();
    // Intervalle rapide pour synchronisation temps réel
    const interval = setInterval(getStatus, 500);

    // Nettoyage lors du démontage
    return () => {
      clearInterval(interval);
      if (websocketRef.current) {
        websocketRef.current.close();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Styles pour les boutons
  const buttonStyle = {
    base: {
      padding: '8px 16px',
      border: 'none',
      borderRadius: '6px',
      fontSize: '0.9rem',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      justifyContent: 'center',
      boxShadow: c2ewTheme.shadows.button,
      textTransform: 'uppercase',
      letterSpacing: '0.5px'
    },
    primary: {
      background: c2ewTheme.colors.accent,
      color: 'white'
    },
    success: {
      background: c2ewTheme.colors.success,
      color: 'white'
    },
    danger: {
      background: c2ewTheme.colors.danger,
      color: 'white'
    },
    warning: {
      background: c2ewTheme.colors.warning,
      color: 'white'
    },
    secondary: {
      background: c2ewTheme.colors.darkBorder,
      color: c2ewTheme.colors.text
    },
    active: {
      background: c2ewTheme.colors.success,
      color: 'white',
      boxShadow: `0 0 10px ${c2ewTheme.colors.success}`
    }
  };

  return (
    <div style={styles.container}>
      {/* Header avec titre et boutons ON/OFF */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        padding: '15px 20px',
        background: c2ewTheme.colors.cardBg,
        borderRadius: '8px',
        border: `1px solid ${c2ewTheme.colors.border}`
      }}>
        <h1 style={{
          margin: 0,
          fontSize: '1.5rem',
          color: c2ewTheme.colors.text,
          display: 'flex',
          alignItems: 'center',
          gap: '10px'
        }}>
          <Radio size={24} />
          C2-EW Platform - Contrôle Radio ICOM IC-R8600
        </h1>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '15px'
        }}>
          {/* Status de connexion */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 12px',
            background: isConnected ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
            borderRadius: '6px',
            border: `1px solid ${isConnected ? c2ewTheme.colors.success : c2ewTheme.colors.danger}`
          }}>
            <div style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: isConnected ? c2ewTheme.colors.success : c2ewTheme.colors.danger
            }}></div>
            <span style={{
              fontSize: '0.8rem',
              color: isConnected ? c2ewTheme.colors.success : c2ewTheme.colors.danger,
              fontWeight: '500'
            }}>
              {isConnected ? `Connecté - ${connectionQuality}` : 'Déconnecté'}
            </span>
          </div>
        </div>
      </div>

      {/* Messages d'alerte compacts */}
      {message.text && (
        <div style={{
          padding: '8px 12px',
          borderRadius: '4px',
          marginBottom: '10px',
          fontSize: '0.8rem',
          backgroundColor: message.type === 'success' ?
            'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
          borderLeft: `3px solid ${message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger}`,
          color: message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger
        }}>
          {message.text}
        </div>
      )}

      {/* Grille principale compacte */}
      <div style={styles.mainGrid}>

        {/* Écran LCD et contrôles de fréquence */}
        <div style={styles.card}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            <h2 style={{
              ...styles.cardTitle,
              margin: 0
            }}>
              <Monitor size={16} />
              LCD - Fréquence
            </h2>

            {/* Petits boutons ON/OFF circulaires en haut à droite */}
            <div style={{
              display: 'flex',
              gap: '8px',
              alignItems: 'center'
            }}>
              <span style={{
                fontSize: '0.7rem',
                color: c2ewTheme.colors.textMuted,
                marginRight: '5px'
              }}>
                POWER
              </span>
              <button
                onClick={() => sendCommand({ power: true })}
                style={{
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  border: 'none',
                  background: c2ewTheme.colors.success,
                  color: 'white',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.7rem',
                  fontWeight: 'bold',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                  transition: 'all 0.2s ease'
                }}
                onMouseOver={(e) => e.target.style.transform = 'scale(1.1)'}
                onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
                title="Allumer le récepteur"
              >
                ON
              </button>
              <button
                onClick={() => sendCommand({ power: false })}
                style={{
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  border: 'none',
                  background: c2ewTheme.colors.danger,
                  color: 'white',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.6rem',
                  fontWeight: 'bold',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                  transition: 'all 0.2s ease'
                }}
                onMouseOver={(e) => e.target.style.transform = 'scale(1.1)'}
                onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
                title="Éteindre le récepteur"
              >
                OFF
              </button>
            </div>
          </div>

          {/* Écran LCD éditable */}
          <div style={styles.lcdDisplay}>
            {isEditingFreq ? (
              <div>
                <input
                  type="text"
                  value={frequencyInput}
                  onChange={(e) => setFrequencyInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') handleFrequencySubmit();
                    if (e.key === 'Escape') handleFrequencyCancel();
                  }}
                  style={styles.frequencyInput}
                  autoFocus
                  placeholder="000.000.000"
                />
                <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                  <button
                    onClick={handleFrequencySubmit}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.success,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✓ VALIDER
                  </button>
                  <button
                    onClick={handleFrequencyCancel}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.danger,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✗ ANNULER
                  </button>
                </div>
              </div>
            ) : (
              <div onClick={handleFrequencyEdit} style={{ cursor: 'pointer' }}>
                <div style={styles.lcdFrequency}>
                  {formatFrequencyLCD(frequency)}
                </div>
                <div style={styles.lcdInfo}>
                  <span>{radioStatus.mode || mode}</span>
                  <span>RSSI: {radioStatus.rssi || -80} dBm</span>
                  <span style={{
                    color: radioStatus.power_on ? c2ewTheme.colors.success : c2ewTheme.colors.danger,
                    fontWeight: 'bold'
                  }}>
                    {radioStatus.power_on ? 'ON' : 'OFF'}
                  </span>
                </div>
                <div style={{
                  fontSize: '0.7rem',
                  color: c2ewTheme.colors.textMuted,
                  marginTop: '3px',
                  display: 'flex',
                  justifyContent: 'space-between'
                }}>
                  <span>AF: {radioStatus.af_gain || afGain}%</span>
                  <span>RF: {radioStatus.rf_gain || rfGain}%</span>
                  {commandInProgress && (
                    <span style={{ color: c2ewTheme.colors.warning }}>⏳</span>
                  )}
                </div>
                <div style={{
                  fontSize: '0.7rem',
                  color: c2ewTheme.colors.textMuted,
                  marginTop: '5px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <span>Cliquez pour éditer la fréquence</span>
                  <span style={{
                    fontSize: '0.6rem',
                    color: userChangingFreq ? c2ewTheme.colors.accent :
                           frequency === radioStatus.frequency ? c2ewTheme.colors.success : c2ewTheme.colors.warning
                  }}>
                    {userChangingFreq ? '🎛️ MOLETTE' :
                     frequency === radioStatus.frequency ? '✓ SYNC' : '⟳ SYNC...'}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Molette rotative et contrôles gains */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr auto 1fr',
            gap: '15px',
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            {/* AF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  AF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {radioStatus.af_gain || afGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={radioStatus.af_gain || afGain}
                onChange={(e) => handleAfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Molette centrale */}
            <div style={{ textAlign: 'center' }}>
              <div style={{
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '8px'
              }}>
                MOLETTE FRÉQ
              </div>
              <div
                className="rotary-knob"
                style={{
                  ...styles.rotaryKnob,
                  transform: `rotate(${knobRotation}deg)`
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  const rect = e.currentTarget.getBoundingClientRect();
                  const centerX = rect.left + rect.width / 2;
                  const clickX = e.clientX;
                  const delta = clickX > centerX ? 1 : -1;
                  handleKnobRotation(delta);
                }}
              >
                <div style={styles.rotaryIndicator}></div>
              </div>
              <div style={{
                fontSize: '0.7rem',
                color: c2ewTheme.colors.textMuted
              }}>
                ±25kHz
              </div>
            </div>

            {/* RF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  RF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {radioStatus.rf_gain || rfGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={radioStatus.rf_gain || rfGain}
                onChange={(e) => handleRfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>
          </div>

          {/* Contrôles rapides de fréquence */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(6, 1fr)',
              gap: '4px',
              marginBottom: '8px'
            }}>
              <button
                onClick={() => handleFrequencyChange(-100000)}
                style={{...buttonStyle.base, ...buttonStyle.danger, padding: '4px 6px', fontSize: '0.65rem'}}
                disabled={loading}
              >
                -100k
              </button>
              <button
                onClick={() => handleFrequencyChange(-25000)}
                style={{...buttonStyle.base, ...buttonStyle.warning, padding: '4px 6px', fontSize: '0.65rem'}}
                disabled={loading}
              >
                -25k
              </button>
              <button
                onClick={() => handleFrequencyChange(-10000)}
                style={{...buttonStyle.base, ...buttonStyle.info, padding: '4px 6px', fontSize: '0.65rem'}}
                disabled={loading}
              >
                -10k
              </button>
              <button
                onClick={() => handleFrequencyChange(10000)}
                style={{...buttonStyle.base, ...buttonStyle.info, padding: '4px 6px', fontSize: '0.65rem'}}
                disabled={loading}
              >
                +10k
              </button>
              <button
                onClick={() => handleFrequencyChange(25000)}
                style={{...buttonStyle.base, ...buttonStyle.warning, padding: '4px 6px', fontSize: '0.65rem'}}
                disabled={loading}
              >
                +25k
              </button>
              <button
                onClick={() => handleFrequencyChange(100000)}
                style={{...buttonStyle.base, ...buttonStyle.success, padding: '4px 6px', fontSize: '0.65rem'}}
                disabled={loading}
              >
                +100k
              </button>
            </div>
          </div>

          {/* Audio Live Récepteur */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              AUDIO LIVE RÉCEPTEUR
            </div>

            {/* Contrôle de volume pour l'audio streaming */}
            <div style={{ marginBottom: '10px' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.7rem' }}>
                  VOLUME PC
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.7rem' }}>
                  {volume}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => {
                  const newVolume = parseInt(e.target.value);
                  setVolume(newVolume);
                  if (gainNodeRef.current) {
                    gainNodeRef.current.gain.value = newVolume / 100;
                  }
                }}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Boutons de contrôle audio */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '6px'
            }}>
              <button
                onClick={toggleStreaming}
                style={{
                  ...buttonStyle.base,
                  ...(isStreaming ? buttonStyle.danger : buttonStyle.success),
                  padding: '8px 12px',
                  fontSize: '0.8rem',
                  fontWeight: 'bold'
                }}
              >
                {isStreaming ? 'STOP' : 'LIVE'}
              </button>
              <button
                onClick={toggleRecording}
                style={{
                  ...buttonStyle.base,
                  ...(isRecording ? buttonStyle.danger : buttonStyle.secondary),
                  padding: '8px 12px',
                  fontSize: '0.8rem',
                  fontWeight: 'bold'
                }}
              >
                {isRecording ? 'STOP REC' : 'REC'}
              </button>
            </div>
          </div>
        </div>




      </div>

      {/* Section Fréquences Cibles - En haut */}
      <div style={{
        marginTop: '15px',
        padding: '15px',
        background: c2ewTheme.colors.cardBg,
        borderRadius: '8px',
        border: `1px solid ${c2ewTheme.colors.border}`
      }}>
        <h2 style={{
          margin: '0 0 15px 0',
          fontSize: '1.1rem',
          color: c2ewTheme.colors.text,
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <Target size={18} />
          Fréquences Cibles
        </h2>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 2fr',
          gap: '20px'
        }}>
          {/* Ajout de fréquence */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px',
              fontWeight: '500'
            }}>
              AJOUTER FRÉQUENCE
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '8px'
            }}>
              <input
                type="text"
                placeholder="Fréquence (Hz)"
                value={newFreqInput}
                onChange={(e) => setNewFreqInput(e.target.value)}
                style={{
                  padding: '8px 12px',
                  background: c2ewTheme.colors.dark,
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
              />
              <input
                type="text"
                placeholder="Commentaire"
                value={newFreqComment}
                onChange={(e) => setNewFreqComment(e.target.value)}
                style={{
                  padding: '8px 12px',
                  background: c2ewTheme.colors.dark,
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
              />
              <button
                onClick={addTargetFrequency}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.success,
                  padding: '8px 12px',
                  fontSize: '0.8rem',
                  fontWeight: '500'
                }}
              >
                AJOUTER
              </button>
            </div>
          </div>

          {/* Liste des fréquences */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px',
              fontWeight: '500'
            }}>
              FRÉQUENCES ENREGISTRÉES
            </div>
            <div style={{
              maxHeight: '150px',
              overflowY: 'auto',
              border: `1px solid ${c2ewTheme.colors.darkBorder}`,
              borderRadius: '6px',
              background: c2ewTheme.colors.dark
            }}>
              {targetFrequencies.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '20px',
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.8rem'
                }}>
                  Aucune fréquence cible
                </div>
              ) : (
                targetFrequencies.map((target, index) => (
                  <div
                    key={index}
                    onClick={() => handleDirectFrequencySet(target.freq)}
                    style={{
                      padding: '8px 12px',
                      borderBottom: index < targetFrequencies.length - 1 ? `1px solid ${c2ewTheme.colors.darkBorder}` : 'none',
                      cursor: 'pointer',
                      background: target.active ? c2ewTheme.colors.accent + '20' : 'transparent',
                      transition: 'all 0.2s ease',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}
                    onMouseOver={(e) => e.target.style.background = c2ewTheme.colors.accent + '10'}
                    onMouseOut={(e) => e.target.style.background = target.active ? c2ewTheme.colors.accent + '20' : 'transparent'}
                  >
                    <div>
                      <div style={{
                        color: c2ewTheme.colors.text,
                        fontSize: '0.8rem',
                        fontWeight: '500'
                      }}>
                        {formatFrequencyLCD(target.freq)} - {target.name}
                      </div>
                      <div style={{
                        color: c2ewTheme.colors.textMuted,
                        fontSize: '0.7rem'
                      }}>
                        {target.comment}
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeTargetFrequency(index);
                      }}
                      style={{
                        ...buttonStyle.base,
                        ...buttonStyle.danger,
                        padding: '4px 6px',
                        fontSize: '0.7rem'
                      }}
                    >
                      ×
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Section Scan */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '10px',
        marginTop: '10px',
        maxWidth: '1200px',
        margin: '10px auto 0'
      }}>

        {/* Scan compact */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Scan size={16} />
            Scan
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            gap: '6px',
            marginBottom: '10px'
          }}>
            <div>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.7rem',
                marginBottom: '3px'
              }}>
                Début (Hz)
              </label>
              <input
                type="number"
                value={scanStartFreq}
                onChange={(e) => setScanStartFreq(parseInt(e.target.value))}
                placeholder="144000000"
                style={{
                  width: '100%',
                  padding: '6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
                disabled={loading || isScanning}
              />
            </div>
            <div>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.7rem',
                marginBottom: '3px'
              }}>
                Fin (Hz)
              </label>
              <input
                type="number"
                value={scanEndFreq}
                onChange={(e) => setScanEndFreq(parseInt(e.target.value))}
                placeholder="146000000"
                style={{
                  width: '100%',
                  padding: '6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
                disabled={loading || isScanning}
              />
            </div>
            <div>
              <label style={{
                display: 'block',
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.7rem',
                marginBottom: '3px'
              }}>
                Pas (Hz)
              </label>
              <input
                type="number"
                value={scanStep}
                onChange={(e) => setScanStep(parseInt(e.target.value))}
                placeholder="25000"
                style={{
                  width: '100%',
                  padding: '6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.dark,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.8rem'
                }}
                disabled={loading || isScanning}
              />
            </div>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '4px'
          }}>
            <button
              onClick={startScan}
              disabled={loading || isScanning}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.success,
                opacity: isScanning ? 0.5 : 1,
                padding: '4px 6px',
                fontSize: '0.7rem'
              }}
            >
              <Play size={12} />
              START
            </button>
            <button
              onClick={stopScan}
              disabled={loading || !isScanning}
              style={{
                ...buttonStyle.base,
                ...buttonStyle.danger,
                opacity: !isScanning ? 0.5 : 1,
                padding: '4px 6px',
                fontSize: '0.7rem'
              }}
            >
              <Square size={12} />
              STOP
            </button>
          </div>

          {isScanning && (
            <div style={{
              marginTop: '6px',
              padding: '4px',
              background: c2ewTheme.colors.warning,
              color: 'white',
              borderRadius: '3px',
              fontSize: '0.6rem',
              textAlign: 'center'
            }}>
              🔍 SCANNING...
            </div>
          )}
        </div>


      </div>






      {/* Section Modulation et Filtres - En bas */}
      <div style={{
        marginTop: '20px',
        padding: '20px',
        background: c2ewTheme.colors.cardBg,
        borderRadius: '8px',
        border: `1px solid ${c2ewTheme.colors.border}`
      }}>
        <h2 style={{
          margin: '0 0 20px 0',
          fontSize: '1.2rem',
          color: c2ewTheme.colors.text,
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <Settings size={18} />
          Modulation & Filtres
        </h2>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '20px'
        }}>
          {/* Modulation */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px',
              fontWeight: '500'
            }}>
              MODULATION
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '6px'
            }}>
              {modes.map(m => (
                <button
                  key={m}
                  onClick={() => handleModeChange(m)}
                  style={{
                    ...buttonStyle.base,
                    ...(mode === m ? buttonStyle.active : buttonStyle.secondary),
                    padding: '8px 12px',
                    fontSize: '0.8rem',
                    fontWeight: '500'
                  }}
                  disabled={loading}
                >
                  {m}
                </button>
              ))}
            </div>
          </div>

          {/* Filtres */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.9rem',
              marginBottom: '10px',
              fontWeight: '500'
            }}>
              FILTRE BANDE PASSANTE
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '6px'
            }}>
              {bandwidthFilters.map(bw => (
                <button
                  key={bw}
                  onClick={() => handleFilterChange(bw)}
                  style={{
                    ...buttonStyle.base,
                    ...(filterBW === bw ? buttonStyle.active : buttonStyle.secondary),
                    padding: '8px 12px',
                    fontSize: '0.8rem',
                    fontWeight: '500'
                  }}
                  disabled={loading}
                >
                  {bw}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Styles CSS intégrés */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }

          input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
          }

          input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-moz-range-thumb {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-webkit-slider-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }

          input[type="range"]::-moz-range-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }
        `
      }} />
    </div>
  );
};

export default C2EWRadioControl;

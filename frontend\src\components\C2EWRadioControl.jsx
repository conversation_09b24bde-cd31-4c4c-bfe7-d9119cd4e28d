import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import '../c2ew-styles.css';
import { 
  Power, 
  Radio, 
  Volume2, 
  Settings, 
  Play, 
  Square, 
  Download,
  Trash2,
  RefreshCw,
  Zap,
  Activity,
  Target,
  Scan,
  Mic,
  MicOff,
  Plus,
  Minus,
  RotateCcw,
  Filter,
  Waves,
  Monitor,
  Headphones,
  VolumeX,
  MapPin,
  Clock,
  Signal,
  Wifi,
  WifiOff
} from 'lucide-react';

// Configuration de l'API
const API_BASE_URL = 'http://localhost:8001';

// Thème C2-EW Platform - Version compacte et sombre
const c2ewTheme = {
  colors: {
    primary: '#000000',       // Noir pur
    secondary: '#111111',     // Noir léger
    accent: '#2563eb',        // Bleu accent
    success: '#10b981',       // Vert
    warning: '#f59e0b',       // Orange
    danger: '#ef4444',        // Rouge
    dark: '#000000',          // Noir pur
    darkCard: '#111111',      // Noir léger pour cartes
    darkBorder: '#333333',    // Bordure gris foncé
    text: '#ffffff',          // Texte blanc pur
    textMuted: '#888888',     // Texte gris
    lcd: '#00ff41',           // Vert LCD vif
    lcdBg: '#000000'          // Fond LCD noir
  },
  shadows: {
    card: '0 2px 8px rgba(0, 0, 0, 0.8)',
    button: '0 1px 3px rgba(0, 0, 0, 0.5)',
    inset: 'inset 0 1px 3px rgba(0, 0, 0, 0.8)'
  }
};

// Styles CSS-in-JS avec thème C2-EW - Optimisé pour 970x700
const styles = {
  container: {
    background: c2ewTheme.colors.dark,
    color: c2ewTheme.colors.text,
    fontFamily: '"Roboto Mono", "Courier New", monospace',
    padding: '8px',
    width: '970px',
    height: '700px',
    overflow: 'hidden',
    boxSizing: 'border-box'
  },

  mainGrid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '8px',
    width: '100%',
    height: '100%'
  },

  card: {
    background: c2ewTheme.colors.darkCard,
    borderRadius: '8px',
    padding: '20px',
    boxShadow: c2ewTheme.shadows.card,
    border: `1px solid ${c2ewTheme.colors.darkBorder}`
  },

  cardTitle: {
    fontSize: '0.9rem',
    fontWeight: 'bold',
    marginBottom: '10px',
    color: c2ewTheme.colors.text,
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },

  lcdDisplay: {
    background: c2ewTheme.colors.lcdBg,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    borderRadius: '6px',
    padding: '15px',
    textAlign: 'center',
    marginBottom: '15px',
    boxShadow: c2ewTheme.shadows.inset,
    position: 'relative'
  },

  lcdFrequency: {
    fontSize: '2rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    color: c2ewTheme.colors.lcd,
    textShadow: `0 0 8px ${c2ewTheme.colors.lcd}`,
    marginBottom: '8px',
    letterSpacing: '1px',
    cursor: 'pointer'
  },

  lcdInfo: {
    fontSize: '0.8rem',
    color: c2ewTheme.colors.lcd,
    opacity: 0.8,
    display: 'flex',
    justifyContent: 'space-between'
  },

  frequencyInput: {
    background: c2ewTheme.colors.lcdBg,
    border: `1px solid ${c2ewTheme.colors.lcd}`,
    borderRadius: '4px',
    padding: '8px',
    color: c2ewTheme.colors.lcd,
    fontSize: '1.5rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    textAlign: 'center',
    width: '100%',
    marginBottom: '10px'
  },

  rotaryKnob: {
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    background: `linear-gradient(145deg, ${c2ewTheme.colors.darkBorder}, ${c2ewTheme.colors.secondary})`,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    cursor: 'pointer',
    position: 'relative',
    margin: '10px auto',
    boxShadow: c2ewTheme.shadows.button
  },

  rotaryIndicator: {
    position: 'absolute',
    top: '5px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '3px',
    height: '20px',
    background: c2ewTheme.colors.accent,
    borderRadius: '2px'
  }
};

const C2EWRadioControl = () => {
  // États pour les contrôles
  const [frequency, setFrequency] = useState(145500000);
  const [mode, setMode] = useState('FM');
  const [afGain, setAfGain] = useState(50);
  const [rfGain, setRfGain] = useState(50);
  const [filterBW, setFilterBW] = useState('2.40K');
  const [squelch, setSquelch] = useState(0);

  // États pour l'édition de fréquence
  const [isEditingFreq, setIsEditingFreq] = useState(false);
  const [frequencyInput, setFrequencyInput] = useState('145.500.000');
  const [knobRotation, setKnobRotation] = useState(0);
  
  // États pour le scan
  const [scanStartFreq, setScanStartFreq] = useState(144000000);
  const [scanEndFreq, setScanEndFreq] = useState(146000000);
  const [scanStep, setScanStep] = useState(25000);
  const [isScanning, setIsScanning] = useState(false);
  
  // États pour l'état du récepteur
  const [radioStatus, setRadioStatus] = useState({
    frequency: 145500000,
    mode: 'FM',
    rssi: -80,
    power_on: false,
    rf_gain: 50,
    af_gain: 50,
    filter_width: 2400,
    squelch: 0,
    volume: 50
  });
  
  // États pour l'enregistrement et audio
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [commandInProgress, setCommandInProgress] = useState(false);
  const [userChangingFreq, setUserChangingFreq] = useState(false);
  const [lastUserFreqChange, setLastUserFreqChange] = useState(0);
  
  // États pour les fréquences cibles
  const [targetFrequencies, setTargetFrequencies] = useState([
    { freq: 145500000, name: 'R1 - Urgence', comment: 'Canal d\'urgence principal', active: false },
    { freq: 145750000, name: 'R2 - Trafic', comment: 'Coordination trafic', active: false },
    { freq: 146000000, name: 'R3 - Coordination', comment: 'Canal de coordination', active: false },
    { freq: 433500000, name: 'UHF - Tactique', comment: 'Fréquence tactique UHF', active: false }
  ]);
  const [newFreqInput, setNewFreqInput] = useState('');
  const [newFreqComment, setNewFreqComment] = useState('');
  
  // États pour les messages et connexion
  const [message, setMessage] = useState({ type: '', text: '' });
  const [isConnected, setIsConnected] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState('EXCELLENT');
  
  // Références pour l'audio
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const gainNodeRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const websocketRef = useRef(null);
  const audioBufferRef = useRef([]);
  
  // Modes de modulation disponibles sur ICOM IC-R8600 - CORRIGÉS
  const modes = ['FM', 'WFM', 'AM', 'USB', 'LSB', 'CW', 'FSK', 'DIGITAL'];
  
  // Filtres de bande passante
  const bandwidthFilters = ['1.80K', '2.40K', '3.00K', '6.00K', '15.0K'];

  // Fonctions utilitaires
  const formatFrequency = (freq) => {
    if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  };

  const formatFrequencyLCD = (freq) => {
    const freqStr = freq.toString().padStart(9, '0');
    return `${freqStr.slice(0, 3)}.${freqStr.slice(3, 6)}.${freqStr.slice(6, 9)}`;
  };

  const parseFrequencyInput = (input) => {
    return parseInt(input.replace(/\./g, ''));
  };

  // Gestionnaire pour l'édition directe de fréquence
  const handleFrequencyEdit = () => {
    setIsEditingFreq(true);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  const handleFrequencySubmit = () => {
    const newFreq = parseFrequencyInput(frequencyInput);
    if (newFreq >= 100000 && newFreq <= 3000000000) {
      setIsEditingFreq(false);

      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage
      setFrequency(newFreq);

      // Envoi immédiat au récepteur
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai - MÊME TIMEOUT QUE LA MOLETTE
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 5000); // 5 secondes comme la molette

      showMessage('success', `Fréquence changée: ${formatFrequencyLCD(newFreq)} Hz`);
    } else {
      showMessage('error', 'Fréquence invalide (100kHz - 3GHz)');
    }
  };

  const handleFrequencyCancel = () => {
    setIsEditingFreq(false);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  // Gestionnaire pour la molette rotative - CONTRÔLE 2ÈME CHIFFRE APRÈS VIRGULE (10Hz)
  const handleKnobRotation = useCallback((delta) => {
    const step = 10; // 10Hz par cran pour contrôler le 2ème chiffre après virgule
    const currentFreq = frequency; // Utiliser la fréquence locale, pas celle du récepteur
    const newFreq = currentFreq + (delta * step);

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      setKnobRotation(prev => prev + delta * 10); // 10 degrés par cran

      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);

      // Envoi immédiat au récepteur sans attendre
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai PLUS LONG pour éviter l'écrasement
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 5000); // 5 secondes pour être sûr que la molette fonctionne

      console.log(`Molette: ${formatFrequencyLCD(newFreq)} Hz`);
    }
  }, [frequency]);

  // Gestionnaire pour le petit bouton rotatif - CONTRÔLE 3ÈME CHIFFRE (1Hz)
  const handleSmallKnobRotation = useCallback((delta) => {
    const step = 1; // 1Hz par cran pour contrôler le 3ème chiffre après virgule
    const currentFreq = frequency; // Utiliser la fréquence locale, pas celle du récepteur
    const newFreq = currentFreq + (delta * step);

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP (MÊME PROTECTION)
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);

      // Envoi immédiat au récepteur sans attendre
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai (MÊME PROTECTION QUE LA MOLETTE)
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 5000); // 5 secondes pour être sûr

      console.log(`Petit bouton: ${formatFrequencyLCD(newFreq)} Hz`);
    }
  }, [frequency]);

  // Fonction pour afficher un message - SÉCURISÉE CONTRE LES OBJETS
  const showMessage = (type, text) => {
    // S'assurer que text est une string
    const messageText = typeof text === 'string' ? text :
                       typeof text === 'object' ? JSON.stringify(text) :
                       String(text);
    setMessage({ type, text: messageText });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  // Fonctions API - ultra-rapide pour molette 1kHz - GESTION ERREUR AMÉLIORÉE
  const sendCommand = async (commandData) => {
    try {
      // Envoi fire-and-forget pour la molette (pas d'attente)
      axios.post(`${API_BASE_URL}/api/command`, commandData, {
        timeout: 500 // Timeout un peu plus long pour éviter les erreurs
      }).then(response => {
        // Succès silencieux pour la fréquence
        if (!commandData.frequency) {
          showMessage('success', 'Commande envoyée');
        }
      }).catch(error => {
        // Ignorer les erreurs de timeout pour la molette et fréquence
        if (!commandData.frequency && !commandData.mode && !commandData.filter) {
          console.error('Erreur commande:', error);
          showMessage('error', 'Erreur de communication');
        } else {
          // Log silencieux pour fréquence, mode, filtres
          console.log('Commande envoyée (erreur ignorée):', commandData);
        }
      });

    } catch (error) {
      // Ignorer complètement les erreurs pour la molette et contrôles fréquents
      if (!commandData.frequency && !commandData.mode && !commandData.filter) {
        console.error('Erreur commande:', error);
        showMessage('error', 'Erreur de communication');
      }
    }
  };



  const getStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/status`);
      const newStatus = response.data;
      setRadioStatus(newStatus);
      setIsConnected(true);

      // NE PAS écraser la fréquence si l'utilisateur utilise les contrôles - PROTECTION RENFORCÉE
      const timeSinceLastChange = Date.now() - lastUserFreqChange;
      if (!userChangingFreq && timeSinceLastChange > 6000 && newStatus.frequency && Math.abs(newStatus.frequency - frequency) > 50) {
        // Synchroniser seulement si :
        // 1. L'utilisateur n'est pas en train de changer la fréquence
        // 2. Plus de 6 secondes depuis le dernier changement utilisateur
        // 3. La différence est significative (>50Hz pour molette 10Hz)
        setFrequency(newStatus.frequency);
        console.log(`Sync récepteur: ${formatFrequencyLCD(newStatus.frequency)} Hz`);
      }

      // Synchroniser les autres valeurs
      if (newStatus.af_gain !== undefined) setAfGain(newStatus.af_gain);
      if (newStatus.rf_gain !== undefined) setRfGain(newStatus.rf_gain);
      if (newStatus.mode) setMode(newStatus.mode);

    } catch (error) {
      console.error('Erreur lecture état:', error);
      setIsConnected(false);
    }
  };

  // Gestionnaires d'événements pour les contrôles de fréquence - MÊME LOGIQUE QUE LA MOLETTE
  const handleFrequencyChange = (delta) => {
    // UTILISER LA MÊME LOGIQUE QUE handleKnobRotation qui fonctionne parfaitement
    const currentFreq = frequency; // Utiliser la fréquence locale, pas celle du récepteur
    const newFreq = currentFreq + delta;

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);

      // Envoi immédiat au récepteur sans attendre
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai - MÊME TIMEOUT QUE LA MOLETTE
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 5000); // 5 secondes comme la molette

      console.log(`Bouton fréquence: ${formatFrequencyLCD(newFreq)} Hz (delta: ${delta})`);
    }
  };

  const handleDirectFrequencySet = (freq) => {
    // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
    setUserChangingFreq(true);
    setLastUserFreqChange(Date.now());

    // Mise à jour immédiate de l'affichage
    setFrequency(freq);

    // Envoi immédiat au récepteur
    sendCommand({ frequency: freq });

    // Mettre à jour les fréquences cibles
    setTargetFrequencies(prev =>
      prev.map(target => ({ ...target, active: target.freq === freq }))
    );

    // Arrêter le flag après un délai - TIMEOUT PLUS LONG POUR FRÉQUENCES CIBLES
    clearTimeout(window.userFreqTimeout);
    window.userFreqTimeout = setTimeout(() => {
      setUserChangingFreq(false);
    }, 7000); // 7 secondes pour fréquences cibles (plus long)

    console.log(`Fréquence cible: ${formatFrequencyLCD(freq)} Hz`);
  };

  // Gestionnaires pour les contrôles audio - IMPACT RÉEL SUR RÉCEPTEUR
  const handleAfGainChange = useCallback((gain) => {
    setAfGain(gain);
    setRadioStatus(prev => ({ ...prev, af_gain: gain }));

    // Debouncing pour éviter trop de commandes
    clearTimeout(window.afGainTimeout);
    window.afGainTimeout = setTimeout(() => {
      // Essayer plusieurs commandes pour compatibilité API
      sendCommand({ af_gain: gain });
      sendCommand({ audio_gain: gain });
      sendCommand({ volume: gain });
      console.log(`AF Gain changé: ${gain}% - Commandes multiples envoyées`);
    }, 200);
  }, []);

  const handleRfGainChange = useCallback((gain) => {
    setRfGain(gain);
    setRadioStatus(prev => ({ ...prev, rf_gain: gain }));

    // Debouncing pour éviter trop de commandes
    clearTimeout(window.rfGainTimeout);
    window.rfGainTimeout = setTimeout(() => {
      // Essayer plusieurs commandes pour compatibilité API
      sendCommand({ rf_gain: gain });
      sendCommand({ radio_gain: gain });
      sendCommand({ input_gain: gain });
      console.log(`RF Gain changé: ${gain}% - Commandes multiples envoyées`);
    }, 200);
  }, []);

  const handleModeChange = (newMode) => {
    setMode(newMode);
    setRadioStatus(prev => ({ ...prev, mode: newMode }));

    // Mapping spécial pour modes problématiques
    const modeMapping = {
      'D-STAR': ['DSTAR', 'D_STAR', 'DIGITAL', 'DV'],
      'FSK': ['FSK', 'RTTY', 'PSK', 'DATA'],
      'USB': ['USB', 'SSB_U'],
      'LSB': ['LSB', 'SSB_L']
    };

    // Essayer le mode direct d'abord
    sendCommand({ mode: newMode });
    sendCommand({ modulation: newMode });
    sendCommand({ demod: newMode });

    // Essayer les variantes spécifiques si disponibles
    if (modeMapping[newMode]) {
      modeMapping[newMode].forEach(variant => {
        sendCommand({ mode: variant });
        sendCommand({ modulation: variant });
        sendCommand({ demod: variant });
      });
    }

    console.log(`Mode changé: ${newMode} - Commandes multiples + variantes envoyées`);
  };

  const handleFilterChange = (filter) => {
    setFilterBW(filter);
    setRadioStatus(prev => ({ ...prev, filter: filter }));

    // Mapping des filtres comme sur le récepteur ICOM IC-R8600
    const filterMapping = {
      'FIL1': 1,  // Filtre 1 (étroit)
      'FIL2': 2,  // Filtre 2 (moyen)
      'FIL3': 3   // Filtre 3 (large)
    };

    // Mapping des largeurs de bande en Hz
    const bandwidthMapping = {
      'FIL1': 2400,   // 2.4 kHz (étroit)
      'FIL2': 6000,   // 6 kHz (moyen)
      'FIL3': 15000   // 15 kHz (large)
    };

    const filterNumber = filterMapping[filter];
    const bandwidth = bandwidthMapping[filter];

    if (filterNumber && bandwidth) {
      // Essayer TOUTES les commandes possibles pour compatibilité maximale
      sendCommand({ filter_width: filterNumber });
      sendCommand({ filter: filterNumber });
      sendCommand({ bw_filter: filterNumber });
      sendCommand({ bandwidth: bandwidth });
      sendCommand({ filter_bandwidth: bandwidth });
      sendCommand({ bw: bandwidth });
      sendCommand({ if_filter: filterNumber });

      console.log(`Filtre changé: ${filter} (${filterNumber}) BW: ${bandwidth}Hz - Toutes commandes envoyées`);
    }
  };

  // Gestionnaires pour le scan - AVEC VALIDATION COMPLÈTE
  const startScan = async () => {
    // Validation des paramètres
    const startFreq = parseInt(scanStartFreq) || 144000000;
    const endFreq = parseInt(scanEndFreq) || 146000000;
    const stepFreq = parseInt(scanStep) || 25000;

    // Validation logique
    if (startFreq >= endFreq) {
      showMessage('error', 'Fréquence de début doit être inférieure à la fréquence de fin');
      return;
    }

    if (stepFreq <= 0 || stepFreq > (endFreq - startFreq)) {
      showMessage('error', 'Pas de fréquence invalide (doit être > 0 et < plage)');
      return;
    }

    if (startFreq < 100000 || endFreq > 3000000000) {
      showMessage('error', 'Fréquences hors limites (100kHz - 3GHz)');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/start`, {
        start_frequency: startFreq,
        end_frequency: endFreq,
        step: stepFreq,
        mode: mode
      });
      setIsScanning(true);
      showMessage('success', response.data.message || 'Scan démarré');
      console.log(`Scan démarré: ${formatFrequencyLCD(startFreq)} → ${formatFrequencyLCD(endFreq)} (pas: ${stepFreq}Hz)`);
    } catch (error) {
      console.error('Erreur scan:', error);
      const errorMsg = error.response?.data?.detail ||
                      error.response?.data?.message ||
                      `Erreur scan (${error.response?.status || 'réseau'})`;
      showMessage('error', errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const stopScan = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/stop`);
      setIsScanning(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt scan');
    }
  };

  // Gestionnaires pour l'audio streaming réel via WebSocket
  const startAudioStream = async () => {
    try {
      // Initialiser le contexte audio
      const context = new (window.AudioContext || window.webkitAudioContext)();
      audioContextRef.current = context;

      // Créer les nœuds audio
      const analyser = context.createAnalyser();
      analyser.fftSize = 256;
      analyserRef.current = analyser;

      const gainNode = context.createGain();
      gainNode.gain.value = volume / 100;
      gainNodeRef.current = gainNode;

      // Connecter à la sortie
      gainNode.connect(context.destination);
      analyser.connect(gainNode);

      // Établir la connexion WebSocket pour l'audio
      const ws = new WebSocket('ws://localhost:8001/ws/audio');
      websocketRef.current = ws;

      ws.onopen = () => {
        console.log('🎵 WebSocket audio connecté');
        showMessage('success', 'Connexion audio établie');
        // Demander le démarrage du streaming
        ws.send(JSON.stringify({
          type: 'start_stream'
        }));
      };

      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);

        if (message.type === 'stream_started') {
          setIsStreaming(true);
          showMessage('success', 'Streaming audio du récepteur démarré');
          startAudioAnalysis();
        } else if (message.type === 'audio_data') {
          // Décoder et jouer les données audio du récepteur
          playAudioData(message.data);
        } else if (message.type === 'stream_stopped') {
          setIsStreaming(false);
          showMessage('info', 'Streaming audio arrêté');
        }
      };

      ws.onclose = () => {
        console.log('🔇 WebSocket audio fermé');
        setIsStreaming(false);
      };

      ws.onerror = (error) => {
        console.error('❌ Erreur WebSocket audio:', error);
        showMessage('error', 'Erreur connexion audio - Vérifiez que le récepteur est connecté à la ligne d\'entrée');
        setIsStreaming(false);
      };

    } catch (error) {
      console.error('❌ Erreur démarrage streaming:', error);
      showMessage('error', 'Erreur démarrage streaming audio');
    }
  };

  // Fonction pour jouer les données audio reçues du récepteur
  const playAudioData = async (audioDataB64) => {
    try {
      if (!audioContextRef.current || !analyserRef.current || !gainNodeRef.current) return;

      // Décoder les données audio base64
      const audioBytes = atob(audioDataB64);
      const audioArray = new Int16Array(audioBytes.length / 2);

      for (let i = 0; i < audioArray.length; i++) {
        audioArray[i] = (audioBytes.charCodeAt(i * 2) & 0xFF) |
                       ((audioBytes.charCodeAt(i * 2 + 1) & 0xFF) << 8);
      }

      // Convertir en Float32Array pour Web Audio API
      const floatArray = new Float32Array(audioArray.length);
      for (let i = 0; i < audioArray.length; i++) {
        floatArray[i] = audioArray[i] / 32768.0;
      }

      // Créer un buffer audio
      const audioBuffer = audioContextRef.current.createBuffer(1, floatArray.length, 48000);
      audioBuffer.getChannelData(0).set(floatArray);

      // Créer une source et la connecter à la chaîne audio
      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;

      // Connecter: source -> analyser -> gain -> destination (haut-parleurs)
      source.connect(analyserRef.current);

      // Ajuster le volume selon le contrôle
      gainNodeRef.current.gain.value = (volume / 100) * (isMuted ? 0 : 1);

      // Jouer le son du récepteur sur les haut-parleurs du PC
      source.start();

    } catch (error) {
      console.error('❌ Erreur lecture audio du récepteur:', error);
    }
  };

  const stopAudioStream = () => {
    // Arrêter le WebSocket
    if (websocketRef.current) {
      websocketRef.current.send(JSON.stringify({
        type: 'stop_stream'
      }));
      websocketRef.current.close();
      websocketRef.current = null;
    }

    // Fermer le contexte audio
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setIsStreaming(false);
    showMessage('success', 'Streaming audio arrêté');
  };

  const startAudioAnalysis = () => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const analyze = () => {
      analyser.getByteFrequencyData(dataArray);
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      setAudioLevel(Math.round((average / 255) * 100));

      if (isStreaming) {
        requestAnimationFrame(analyze);
      }
    };

    analyze();
  };

  // Gestionnaires pour l'enregistrement
  const startRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: 'AF'
      });
      setIsRecording(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur enregistrement');
    }
  };

  const stopRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsRecording(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt enregistrement');
    }
  };

  // Gestionnaires pour les fréquences cibles
  const addTargetFrequency = () => {
    if (newFreqInput) {
      const freq = parseInt(newFreqInput);
      if (freq >= 100000 && freq <= 3000000000) {
        setTargetFrequencies(prev => [...prev, {
          freq: freq,
          name: `F${prev.length + 1}`,
          comment: newFreqComment || 'Nouvelle fréquence',
          active: false
        }]);
        setNewFreqInput('');
        setNewFreqComment('');
        showMessage('success', 'Fréquence cible ajoutée');
      }
    }
  };

  const removeTargetFrequency = (index) => {
    setTargetFrequencies(prev => prev.filter((_, i) => i !== index));
    showMessage('success', 'Fréquence cible supprimée');
  };

  // Gestionnaires pour l'alimentation
  const handlePowerOn = () => {
    sendCommand({ power_on: true });
  };

  const handlePowerOff = () => {
    sendCommand({ power_on: false });
  };

  // Gestionnaires pour l'audio streaming - corrigés
  const toggleStreaming = () => {
    if (isStreaming) {
      stopAudioStream();
    } else {
      startAudioStream();
    }
  };

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };



  // Gestionnaire pour la molette de la souris - corrigé
  useEffect(() => {
    const handleWheel = (e) => {
      // Vérifier si la souris est sur la molette rotative
      const knobElement = document.querySelector('.rotary-knob');
      if (knobElement && knobElement.contains(e.target)) {
        e.preventDefault();
        e.stopPropagation();
        const delta = e.deltaY > 0 ? -1 : 1;
        handleKnobRotation(delta);
        return false;
      }
    };

    // Ajouter l'événement avec { passive: false } pour permettre preventDefault
    document.addEventListener('wheel', handleWheel, { passive: false, capture: true });

    return () => {
      document.removeEventListener('wheel', handleWheel, { capture: true });
    };
  }, [handleKnobRotation]);

  // Effet pour charger les données au démarrage
  useEffect(() => {
    getStatus();
    // Intervalle rapide pour synchronisation temps réel
    const interval = setInterval(getStatus, 500);

    // Nettoyage lors du démontage
    return () => {
      clearInterval(interval);
      if (websocketRef.current) {
        websocketRef.current.close();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Styles pour les boutons
  const buttonStyle = {
    base: {
      padding: '8px 16px',
      border: 'none',
      borderRadius: '6px',
      fontSize: '0.9rem',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      justifyContent: 'center',
      boxShadow: c2ewTheme.shadows.button,
      textTransform: 'uppercase',
      letterSpacing: '0.5px'
    },
    primary: {
      background: c2ewTheme.colors.accent,
      color: 'white'
    },
    success: {
      background: c2ewTheme.colors.success,
      color: 'white'
    },
    danger: {
      background: c2ewTheme.colors.danger,
      color: 'white'
    },
    warning: {
      background: c2ewTheme.colors.warning,
      color: 'white'
    },
    secondary: {
      background: c2ewTheme.colors.darkBorder,
      color: c2ewTheme.colors.text
    },
    active: {
      background: c2ewTheme.colors.success,
      color: 'white',
      boxShadow: `0 0 10px ${c2ewTheme.colors.success}`
    }
  };

  return (
    <div style={styles.container}>
      {/* Messages d'alerte compacts */}
      {message.text && (
        <div style={{
          padding: '8px 12px',
          borderRadius: '4px',
          marginBottom: '10px',
          fontSize: '0.8rem',
          backgroundColor: message.type === 'success' ?
            'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
          borderLeft: `3px solid ${message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger}`,
          color: message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger
        }}>
          {message.text}
        </div>
      )}

      {/* Grille principale compacte */}
      <div style={styles.mainGrid}>

        {/* Écran LCD et contrôles de fréquence */}
        <div style={styles.card}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            <h2 style={{...styles.cardTitle, margin: 0}}>
              <Monitor size={16} />
              LCD - Fréquence
            </h2>

            {/* Boutons ON/OFF dans la même ligne */}
            <div style={{
              display: 'flex',
              gap: '8px'
            }}>
              <button
                onClick={handlePowerOn}
                disabled={loading || radioStatus.power_on}
                style={{
                  width: '45px',
                  height: '22px',
                  borderRadius: '11px',
                  border: `2px solid ${radioStatus.power_on ? c2ewTheme.colors.success : c2ewTheme.colors.darkBorder}`,
                  background: radioStatus.power_on ? c2ewTheme.colors.success : 'transparent',
                  color: radioStatus.power_on ? 'white' : c2ewTheme.colors.success,
                  fontSize: '0.65rem',
                  cursor: radioStatus.power_on ? 'default' : 'pointer',
                  opacity: radioStatus.power_on ? 1 : 0.8,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease',
                  boxShadow: radioStatus.power_on ? `0 0 8px ${c2ewTheme.colors.success}40` : 'none'
                }}
              >
                <Power size={10} style={{ marginRight: '2px' }} />
                ON
              </button>
              <button
                onClick={handlePowerOff}
                disabled={loading || !radioStatus.power_on}
                style={{
                  width: '45px',
                  height: '22px',
                  borderRadius: '11px',
                  border: `2px solid ${!radioStatus.power_on ? c2ewTheme.colors.danger : c2ewTheme.colors.darkBorder}`,
                  background: !radioStatus.power_on ? c2ewTheme.colors.danger : 'transparent',
                  color: !radioStatus.power_on ? 'white' : c2ewTheme.colors.danger,
                  fontSize: '0.65rem',
                  cursor: !radioStatus.power_on ? 'default' : 'pointer',
                  opacity: !radioStatus.power_on ? 1 : 0.8,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease',
                  boxShadow: !radioStatus.power_on ? `0 0 8px ${c2ewTheme.colors.danger}40` : 'none'
                }}
              >
                <Power size={10} style={{ marginRight: '2px' }} />
                OFF
              </button>
            </div>
          </div>

          {/* Écran LCD éditable avec boutons ON/OFF intégrés */}
          <div style={styles.lcdDisplay}>
            {isEditingFreq ? (
              <div>
                <input
                  type="text"
                  value={frequencyInput}
                  onChange={(e) => setFrequencyInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') handleFrequencySubmit();
                    if (e.key === 'Escape') handleFrequencyCancel();
                  }}
                  style={styles.frequencyInput}
                  autoFocus
                  placeholder="000.000.000"
                />
                <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                  <button
                    onClick={handleFrequencySubmit}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.success,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✓ VALIDER
                  </button>
                  <button
                    onClick={handleFrequencyCancel}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.danger,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✗ ANNULER
                  </button>
                </div>
              </div>
            ) : (
              <div style={{ position: 'relative' }}>
                <div onClick={handleFrequencyEdit} style={{ cursor: 'pointer' }}>
                  <div style={styles.lcdFrequency}>
                    {formatFrequencyLCD(frequency)}
                  </div>
                  <div style={styles.lcdInfo}>
                    <span>{radioStatus.mode || mode}</span>
                    <span>RSSI: {radioStatus.rssi || -80} dBm</span>
                    <span style={{
                      color: radioStatus.power_on ? c2ewTheme.colors.success : c2ewTheme.colors.danger,
                      fontWeight: 'bold'
                    }}>
                      {radioStatus.power_on ? 'ON' : 'OFF'}
                    </span>
                  </div>
                  <div style={{
                    fontSize: '0.7rem',
                    color: c2ewTheme.colors.textMuted,
                    marginTop: '3px',
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}>
                    <span>AF: {radioStatus.af_gain || afGain}%</span>
                    <span>RF: {radioStatus.rf_gain || rfGain}%</span>
                    {commandInProgress && (
                      <span style={{ color: c2ewTheme.colors.warning }}>⏳</span>
                    )}
                  </div>
                  <div style={{
                    fontSize: '0.7rem',
                    color: c2ewTheme.colors.textMuted,
                    marginTop: '5px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <span>Cliquez pour éditer la fréquence</span>
                    <span style={{
                      fontSize: '0.6rem',
                      color: userChangingFreq ? c2ewTheme.colors.accent :
                             frequency === radioStatus.frequency ? c2ewTheme.colors.success : c2ewTheme.colors.warning
                    }}>
                      {userChangingFreq ? '🎛️ MOLETTE' :
                       frequency === radioStatus.frequency ? '✓ SYNC' : '⟳ SYNC...'}
                    </span>
                  </div>
                </div>


              </div>
            )}
          </div>

          {/* Molette rotative et contrôles gains */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr auto 1fr',
            gap: '15px',
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            {/* AF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  AF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {radioStatus.af_gain || afGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={radioStatus.af_gain || afGain}
                onChange={(e) => handleAfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Molette centrale */}
            <div style={{ textAlign: 'center' }}>
              <div style={{
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '8px'
              }}>
                MOLETTE FRÉQ
              </div>
              <div
                className="rotary-knob"
                style={{
                  ...styles.rotaryKnob,
                  transform: `rotate(${knobRotation}deg)`
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  const rect = e.currentTarget.getBoundingClientRect();
                  const centerX = rect.left + rect.width / 2;
                  const clickX = e.clientX;
                  const delta = clickX > centerX ? 1 : -1;
                  handleKnobRotation(delta);
                }}
              >
                <div style={styles.rotaryIndicator}></div>
              </div>
              <div style={{
                fontSize: '0.7rem',
                color: c2ewTheme.colors.textMuted
              }}>
                ±10Hz
              </div>
            </div>

            {/* RF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  RF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {radioStatus.rf_gain || rfGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={radioStatus.rf_gain || rfGain}
                onChange={(e) => handleRfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>
          </div>

          {/* Contrôles rapides de fréquence */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '4px',
              marginBottom: '6px'
            }}>
              <button
                onClick={() => handleFrequencyChange(-100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -100k
              </button>
              <button
                onClick={() => handleFrequencyChange(-25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -25k
              </button>
              <button
                onClick={() => handleFrequencyChange(-10000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -10k
              </button>
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '4px'
            }}>
              <button
                onClick={() => handleFrequencyChange(10000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +10k
              </button>
              <button
                onClick={() => handleFrequencyChange(25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +25k
              </button>
              <button
                onClick={() => handleFrequencyChange(100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +100k
              </button>
            </div>
          </div>

          {/* Audio Live Récepteur */}
          <div style={{ marginTop: '10px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              AUDIO LIVE RÉCEPTEUR
            </div>

            {/* Contrôle Volume PC */}
            <div style={{ marginBottom: '10px' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.7rem' }}>
                  VOLUME PC
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.7rem' }}>
                  {volume}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => {
                  const newVolume = parseInt(e.target.value);
                  setVolume(newVolume);
                  if (gainNodeRef.current) {
                    gainNodeRef.current.gain.value = newVolume / 100;
                  }
                }}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Boutons LIVE et REC */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '6px'
            }}>
              <button
                onClick={toggleStreaming}
                style={{
                  ...buttonStyle.base,
                  ...(isStreaming ? buttonStyle.danger : buttonStyle.success),
                  padding: '6px 8px',
                  fontSize: '0.7rem'
                }}
              >
                <Play size={12} />
                {isStreaming ? 'STOP' : 'LIVE'}
              </button>
              <button
                onClick={toggleRecording}
                style={{
                  ...buttonStyle.base,
                  ...(isRecording ? buttonStyle.danger : buttonStyle.secondary),
                  padding: '6px 8px',
                  fontSize: '0.7rem'
                }}
              >
                <Square size={12} />
                {isRecording ? 'STOP' : 'REC'}
              </button>
            </div>
          </div>
        </div>

        {/* Contrôles Audio et Filtres */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Volume2 size={16} />
            Audio & Filtres
          </h2>

          {/* Modulation */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              MODULATION
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '4px'
            }}>
              {modes.slice(0, 8).map(m => (
                <button
                  key={m}
                  onClick={() => handleModeChange(m)}
                  style={{
                    ...buttonStyle.base,
                    ...(mode === m ? buttonStyle.active : buttonStyle.secondary),
                    padding: '4px 6px',
                    fontSize: '0.7rem'
                  }}
                  disabled={loading}
                >
                  {m}
                </button>
              ))}
            </div>
          </div>

          {/* Filtres BW - Comme sur le récepteur réel */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              FILTRES BW
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '4px'
            }}>
              {['FIL1', 'FIL2', 'FIL3'].map((filter, index) => (
                <button
                  key={filter}
                  onClick={() => handleFilterChange(filter)}
                  style={{
                    ...buttonStyle.base,
                    ...(filterBW === filter ? buttonStyle.active : buttonStyle.secondary),
                    padding: '4px 6px',
                    fontSize: '0.7rem'
                  }}
                  disabled={loading}
                >
                  {filter}
                </button>
              ))}
            </div>
          </div>

          {/* Fréquences Cibles - Déplacées en haut */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              FRÉQUENCES CIBLES
            </div>

            {/* Ajout rapide de fréquence avec commentaire */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '4px',
              marginBottom: '8px'
            }}>
              <input
                type="text"
                value={newFreqInput}
                onChange={(e) => setNewFreqInput(e.target.value)}
                placeholder="145500000"
                style={{
                  padding: '4px 6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.darkCard,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.7rem'
                }}
              />
              <input
                type="text"
                value={newFreqComment}
                onChange={(e) => setNewFreqComment(e.target.value)}
                placeholder="Commentaire - ex: Canal d'urgence"
                style={{
                  padding: '4px 6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.darkCard,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.7rem'
                }}
              />
              <button
                onClick={addTargetFrequency}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.success,
                  padding: '4px 8px',
                  fontSize: '0.7rem'
                }}
              >
                <Plus size={10} />
                AJOUTER
              </button>
            </div>

            {/* Liste compacte des fréquences */}
            <div style={{
              maxHeight: '120px',
              overflowY: 'auto',
              border: `1px solid ${c2ewTheme.colors.darkBorder}`,
              borderRadius: '4px',
              background: c2ewTheme.colors.dark
            }}>
              {targetFrequencies.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  color: c2ewTheme.colors.textMuted,
                  padding: '8px',
                  fontSize: '0.7rem'
                }}>
                  Aucune fréquence
                </div>
              ) : (
                targetFrequencies.map((target, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '4px 6px',
                      borderBottom: index < targetFrequencies.length - 1 ?
                        `1px solid ${c2ewTheme.colors.darkBorder}` : 'none',
                      background: target.active ?
                        c2ewTheme.colors.success + '20' : 'transparent',
                      cursor: 'pointer',
                      fontSize: '0.7rem'
                    }}
                    onClick={() => handleDirectFrequencySet(target.freq)}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <div style={{
                          color: target.active ? c2ewTheme.colors.success : c2ewTheme.colors.text,
                          fontWeight: 'bold'
                        }}>
                          {formatFrequency(target.freq)}
                        </div>
                        <div style={{
                          color: c2ewTheme.colors.textMuted,
                          fontSize: '0.6rem'
                        }}>
                          {target.comment}
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeTargetFrequency(index);
                        }}
                        style={{
                          ...buttonStyle.base,
                          ...buttonStyle.danger,
                          padding: '2px 4px',
                          fontSize: '0.6rem'
                        }}
                      >
                        <Trash2 size={8} />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Section SCAN PROGRAMMABLE */}
            <div style={{ marginTop: '12px' }}>
              <div style={{
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '8px'
              }}>
                SCAN PROGRAMMABLE
              </div>

              {/* Champs avec labels */}
              <div style={{ marginBottom: '6px' }}>
                <div style={{
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.6rem',
                  marginBottom: '2px'
                }}>
                  DÉBUT (Hz)
                </div>
                <input
                  type="number"
                  value={scanStartFreq || 144000000}
                  onChange={(e) => setScanStartFreq(parseInt(e.target.value))}
                  placeholder="144000000"
                  style={{
                    width: '100%',
                    padding: '4px 6px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.7rem'
                  }}
                />
              </div>

              <div style={{ marginBottom: '6px' }}>
                <div style={{
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.6rem',
                  marginBottom: '2px'
                }}>
                  FIN (Hz)
                </div>
                <input
                  type="number"
                  value={scanEndFreq || 146000000}
                  onChange={(e) => setScanEndFreq(parseInt(e.target.value))}
                  placeholder="146000000"
                  style={{
                    width: '100%',
                    padding: '4px 6px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.7rem'
                  }}
                />
              </div>

              <div style={{ marginBottom: '8px' }}>
                <div style={{
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.6rem',
                  marginBottom: '2px'
                }}>
                  PAS (Hz)
                </div>
                <input
                  type="number"
                  value={scanStep || 25000}
                  onChange={(e) => setScanStep(parseInt(e.target.value))}
                  placeholder="25000"
                  style={{
                    width: '100%',
                    padding: '4px 6px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.7rem'
                  }}
                />
              </div>

              {/* Boutons de contrôle */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '4px'
              }}>
                <button
                  onClick={startScan}
                  disabled={loading || isScanning}
                  style={{
                    ...buttonStyle.base,
                    ...(isScanning ? buttonStyle.active : buttonStyle.success),
                    padding: '4px 8px',
                    fontSize: '0.7rem'
                  }}
                >
                  <Play size={10} />
                  {isScanning ? 'SCANNING...' : 'START'}
                </button>
                <button
                  onClick={stopScan}
                  disabled={loading || !isScanning}
                  style={{
                    ...buttonStyle.base,
                    ...buttonStyle.danger,
                    padding: '4px 8px',
                    fontSize: '0.7rem'
                  }}
                >
                  <Square size={10} />
                  STOP
                </button>
              </div>

              {/* Indicateur de statut */}
              {isScanning && (
                <div style={{
                  marginTop: '6px',
                  padding: '4px',
                  background: c2ewTheme.colors.warning,
                  color: 'white',
                  borderRadius: '3px',
                  fontSize: '0.6rem',
                  textAlign: 'center'
                }}>
                  🔍 SCAN EN COURS...
                </div>
              )}
            </div>
          </div>


        </div>

      </div>




      {/* Styles CSS intégrés */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }

          input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
          }

          input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-moz-range-thumb {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-webkit-slider-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }

          input[type="range"]::-moz-range-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }
        `
      }} />
    </div>
  );
};

export default C2EWRadioControl;

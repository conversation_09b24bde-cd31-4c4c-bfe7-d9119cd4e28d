import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import '../c2ew-styles.css';
import { 
  Power, 
  Radio, 
  Volume2, 
  Settings, 
  Play, 
  Square, 
  Download,
  Trash2,
  RefreshCw,
  Zap,
  Activity,
  Target,
  Scan,
  Mic,
  MicOff,
  Plus,
  Minus,
  RotateCcw,
  Filter,
  Waves,
  Monitor,
  Headphones,
  VolumeX,
  MapPin,
  Clock,
  Signal,
  Wifi,
  WifiOff
} from 'lucide-react';

// Configuration de l'API
const API_BASE_URL = 'http://localhost:8001';

// Thème C2-EW Platform - Version compacte et sombre
const c2ewTheme = {
  colors: {
    primary: '#000000',       // Noir pur
    secondary: '#111111',     // Noir léger
    accent: '#2563eb',        // Bleu accent
    success: '#10b981',       // Vert
    warning: '#f59e0b',       // Orange
    danger: '#ef4444',        // Rouge
    dark: '#000000',          // Noir pur
    darkCard: '#111111',      // Noir léger pour cartes
    darkBorder: '#333333',    // Bordure gris foncé
    text: '#ffffff',          // Texte blanc pur
    textMuted: '#888888',     // Texte gris
    lcd: '#00ff41',           // Vert LCD vif
    lcdBg: '#000000'          // Fond LCD noir
  },
  shadows: {
    card: '0 2px 8px rgba(0, 0, 0, 0.8)',
    button: '0 1px 3px rgba(0, 0, 0, 0.5)',
    inset: 'inset 0 1px 3px rgba(0, 0, 0, 0.8)'
  }
};

// Styles CSS-in-JS avec thème C2-EW - Optimisé pour 970x700
const styles = {
  container: {
    background: c2ewTheme.colors.dark,
    color: c2ewTheme.colors.text,
    fontFamily: '"Roboto Mono", "Courier New", monospace',
    padding: '8px',
    width: '970px',
    height: '700px',
    overflow: 'hidden',
    boxSizing: 'border-box'
  },

  mainGrid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '8px',
    width: '100%',
    height: '100%'
  },

  card: {
    background: c2ewTheme.colors.darkCard,
    borderRadius: '8px',
    padding: '20px',
    boxShadow: c2ewTheme.shadows.card,
    border: `1px solid ${c2ewTheme.colors.darkBorder}`
  },

  cardTitle: {
    fontSize: '0.9rem',
    fontWeight: 'bold',
    marginBottom: '10px',
    color: c2ewTheme.colors.text,
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },

  lcdDisplay: {
    background: c2ewTheme.colors.lcdBg,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    borderRadius: '6px',
    padding: '15px',
    textAlign: 'center',
    marginBottom: '15px',
    boxShadow: c2ewTheme.shadows.inset,
    position: 'relative'
  },

  lcdFrequency: {
    fontSize: '2rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    color: c2ewTheme.colors.lcd,
    textShadow: `0 0 8px ${c2ewTheme.colors.lcd}`,
    marginBottom: '8px',
    letterSpacing: '1px',
    cursor: 'pointer'
  },

  lcdInfo: {
    fontSize: '0.8rem',
    color: c2ewTheme.colors.lcd,
    opacity: 0.8,
    display: 'flex',
    justifyContent: 'space-between'
  },

  frequencyInput: {
    background: c2ewTheme.colors.lcdBg,
    border: `1px solid ${c2ewTheme.colors.lcd}`,
    borderRadius: '4px',
    padding: '8px',
    color: c2ewTheme.colors.lcd,
    fontSize: '1.5rem',
    fontFamily: '"Orbitron", "Roboto Mono", monospace',
    textAlign: 'center',
    width: '100%',
    marginBottom: '10px'
  },

  rotaryKnob: {
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    background: `linear-gradient(145deg, ${c2ewTheme.colors.darkBorder}, ${c2ewTheme.colors.secondary})`,
    border: `2px solid ${c2ewTheme.colors.darkBorder}`,
    cursor: 'pointer',
    position: 'relative',
    margin: '10px auto',
    boxShadow: c2ewTheme.shadows.button
  },

  rotaryIndicator: {
    position: 'absolute',
    top: '5px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '3px',
    height: '20px',
    background: c2ewTheme.colors.accent,
    borderRadius: '2px'
  }
};

const C2EWRadioControl = () => {
  // États pour les contrôles
  const [frequency, setFrequency] = useState(145500000); // FRÉQUENCE LOCALE MAÎTRE
  const [isInitialized, setIsInitialized] = useState(false); // Flag d'initialisation
  const [mode, setMode] = useState('FM');
  const [afGain, setAfGain] = useState(50);
  const [rfGain, setRfGain] = useState(50);
  const [filterBW, setFilterBW] = useState('2.40K');
  const [squelch, setSquelch] = useState(0);

  // États pour l'édition de fréquence
  const [isEditingFreq, setIsEditingFreq] = useState(false);
  const [frequencyInput, setFrequencyInput] = useState('145.500.000');
  const [knobRotation, setKnobRotation] = useState(0);
  
  // États pour le scan
  const [scanStartFreq, setScanStartFreq] = useState(144000000);
  const [scanEndFreq, setScanEndFreq] = useState(146000000);
  const [scanStep, setScanStep] = useState(25000);
  const [isScanning, setIsScanning] = useState(false);
  
  // États pour l'état du récepteur
  const [radioStatus, setRadioStatus] = useState({
    frequency: 145500000,
    mode: 'FM',
    rssi: -80,
    power_on: false,
    rf_gain: 50,
    af_gain: 50,
    filter_width: 2400,
    squelch: 0,
    volume: 50
  });
  
  // États pour l'enregistrement et audio
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [audioInitialized, setAudioInitialized] = useState(false);
  const [loading, setLoading] = useState(false);
  const [commandInProgress, setCommandInProgress] = useState(false);
  const [userChangingFreq, setUserChangingFreq] = useState(false);
  const [lastUserFreqChange, setLastUserFreqChange] = useState(0);
  const [userChangingAF, setUserChangingAF] = useState(false);
  const [userChangingRF, setUserChangingRF] = useState(false);
  
  // États pour les fréquences cibles
  const [targetFrequencies, setTargetFrequencies] = useState([
    { freq: 145500000, name: 'R1 - Urgence', comment: 'Canal d\'urgence principal', active: false },
    { freq: 145750000, name: 'R2 - Trafic', comment: 'Coordination trafic', active: false },
    { freq: 146000000, name: 'R3 - Coordination', comment: 'Canal de coordination', active: false },
    { freq: 433500000, name: 'UHF - Tactique', comment: 'Fréquence tactique UHF', active: false }
  ]);
  const [newFreqInput, setNewFreqInput] = useState('');
  const [newFreqComment, setNewFreqComment] = useState('');
  
  // États pour les messages et connexion
  const [message, setMessage] = useState({ type: '', text: '' });
  const [isConnected, setIsConnected] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState('EXCELLENT');
  
  // Références pour l'audio
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const gainNodeRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const websocketRef = useRef(null);
  const audioBufferRef = useRef([]);
  
  // Modes de modulation disponibles sur ICOM IC-R8600 - CORRIGÉS
  const modes = ['FM', 'WFM', 'AM', 'USB', 'LSB', 'CW', 'FSK', 'DIGITAL'];
  
  // Filtres de bande passante
  const bandwidthFilters = ['1.80K', '2.40K', '3.00K', '6.00K', '15.0K'];

  // Fonctions utilitaires
  const formatFrequency = (freq) => {
    if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  };

  const formatFrequencyLCD = (freq) => {
    const freqStr = freq.toString().padStart(9, '0');
    return `${freqStr.slice(0, 3)}.${freqStr.slice(3, 6)}.${freqStr.slice(6, 9)}`;
  };

  const parseFrequencyInput = (input) => {
    return parseInt(input.replace(/\./g, ''));
  };

  // Gestionnaire pour l'édition directe de fréquence
  const handleFrequencyEdit = () => {
    setIsEditingFreq(true);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  const handleFrequencySubmit = () => {
    const newFreq = parseFrequencyInput(frequencyInput);
    if (newFreq >= 100000 && newFreq <= 3000000000) {
      setIsEditingFreq(false);

      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage
      setFrequency(newFreq);

      // Envoi immédiat au récepteur
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai - MÊME TIMEOUT QUE LA MOLETTE
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 5000); // 5 secondes comme la molette

      showMessage('success', `Fréquence changée: ${formatFrequencyLCD(newFreq)} Hz`);
    } else {
      showMessage('error', 'Fréquence invalide (100kHz - 3GHz)');
    }
  };

  const handleFrequencyCancel = () => {
    setIsEditingFreq(false);
    setFrequencyInput(formatFrequencyLCD(frequency));
  };

  // Gestionnaire pour la molette rotative - CONTRÔLE 2ÈME CHIFFRE APRÈS VIRGULE (10Hz)
  const handleKnobRotation = useCallback((delta) => {
    const step = 10; // 10Hz par cran pour contrôler le 2ème chiffre après virgule
    const currentFreq = frequency; // Utiliser la fréquence locale, pas celle du récepteur
    const newFreq = currentFreq + (delta * step);

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      setKnobRotation(prev => prev + delta * 10); // 10 degrés par cran

      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);

      // Envoi immédiat au récepteur sans attendre
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai ULTRA-LONG pour éviter l'écrasement
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
        console.log(`🔓 Flag molette désactivé après 8 secondes`);
      }, 8000); // 8 secondes pour être sûr que la molette fonctionne

      console.log(`🎛️ Molette: ${formatFrequencyLCD(newFreq)} Hz (delta: ${delta * step}Hz, flag: ${userChangingFreq}, timestamp: ${Date.now()})`);
    }
  }, [frequency]);

  // Gestionnaire pour le petit bouton rotatif - CONTRÔLE 3ÈME CHIFFRE (1Hz)
  const handleSmallKnobRotation = useCallback((delta) => {
    const step = 1; // 1Hz par cran pour contrôler le 3ème chiffre après virgule
    const currentFreq = frequency; // Utiliser la fréquence locale, pas celle du récepteur
    const newFreq = currentFreq + (delta * step);

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP (MÊME PROTECTION)
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);

      // Envoi immédiat au récepteur sans attendre
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai (MÊME PROTECTION QUE LA MOLETTE)
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
      }, 5000); // 5 secondes pour être sûr

      console.log(`Petit bouton: ${formatFrequencyLCD(newFreq)} Hz`);
    }
  }, [frequency]);

  // Fonction pour afficher un message - SÉCURISÉE CONTRE LES OBJETS
  const showMessage = (type, text) => {
    // S'assurer que text est une string
    const messageText = typeof text === 'string' ? text :
                       typeof text === 'object' ? JSON.stringify(text) :
                       String(text);
    setMessage({ type, text: messageText });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  // Fonctions API - ultra-rapide pour molette 1kHz - GESTION ERREUR AMÉLIORÉE
  const sendCommand = async (commandData) => {
    try {
      // Debug spécial pour commandes POWER
      const isPowerCommand = commandData.power !== undefined ||
                            commandData.power_on !== undefined ||
                            commandData.pwr !== undefined ||
                            commandData.cmd?.includes('POWER');

      if (isPowerCommand) {
        console.log('🔌 ENVOI COMMANDE POWER:', commandData);
      }

      // Envoi avec gestion de réponse
      const response = await axios.post(`${API_BASE_URL}/api/command`, commandData, {
        timeout: isPowerCommand ? 2000 : 500 // Plus de temps pour POWER
      });

      if (isPowerCommand) {
        console.log('🔌 RÉPONSE SERVEUR POWER:', response.data);
        showMessage('success', `Commande POWER OK: ${JSON.stringify(commandData)}`);
      } else if (!commandData.frequency) {
        showMessage('success', 'Commande envoyée');
      }

    } catch (error) {
      if (isPowerCommand) {
        console.error('❌ ERREUR COMMANDE POWER:', error);
        console.error('❌ Détails erreur:', error.response?.data);
        showMessage('error', `Erreur POWER: ${error.response?.data?.detail || error.message}`);
      } else if (!commandData.frequency && !commandData.mode && !commandData.filter) {
        console.error('Erreur commande:', error);
        showMessage('error', 'Erreur de communication');
      } else {
        // Log silencieux pour fréquence, mode, filtres
        console.log('Commande envoyée (erreur ignorée):', commandData);
      }
    }
  };



  // FONCTION DÉSACTIVÉE - Plus de synchronisation automatique de fréquence
  const getStatus = async () => {
    console.log(`🚫 getStatus() DÉSACTIVÉE - Interface MAÎTRE absolu de la fréquence`);
    // Cette fonction ne fait plus rien pour éviter l'écrasement de fréquence
    setIsConnected(true);
  };

  // Fonction pour synchroniser SEULEMENT les gains (manuelle)
  const syncGainsOnly = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/status`);
      const newStatus = response.data;

      // Synchroniser SEULEMENT les gains/mode (JAMAIS la fréquence)
      if (newStatus.af_gain !== undefined && !userChangingAF) {
        setAfGain(newStatus.af_gain);
        console.log(`📡 Sync manuelle AF Gain: ${newStatus.af_gain}%`);
      }
      if (newStatus.rf_gain !== undefined && !userChangingRF) {
        setRfGain(newStatus.rf_gain);
        console.log(`📡 Sync manuelle RF Gain: ${newStatus.rf_gain}%`);
      }
      if (newStatus.mode) {
        setMode(newStatus.mode);
        console.log(`📡 Sync manuelle Mode: ${newStatus.mode}`);
      }

      console.log(`✅ Sync manuelle terminée - Fréquence locale PRÉSERVÉE: ${formatFrequencyLCD(frequency)} Hz`);

    } catch (error) {
      console.error('Erreur sync manuelle:', error);
    }
  };

  // Gestionnaires d'événements pour les contrôles de fréquence - MÊME LOGIQUE QUE LA MOLETTE
  const handleFrequencyChange = (delta) => {
    // UTILISER LA MÊME LOGIQUE QUE handleKnobRotation qui fonctionne parfaitement
    const currentFreq = frequency; // Utiliser la fréquence locale, pas celle du récepteur
    const newFreq = currentFreq + delta;

    if (newFreq >= 100000 && newFreq <= 3000000000) {
      // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
      setUserChangingFreq(true);
      setLastUserFreqChange(Date.now());

      // Mise à jour immédiate de l'affichage LCD
      setFrequency(newFreq);

      // Envoi immédiat au récepteur sans attendre
      sendCommand({ frequency: newFreq });

      // Arrêter le flag après un délai - MÊME TIMEOUT QUE LA MOLETTE
      clearTimeout(window.userFreqTimeout);
      window.userFreqTimeout = setTimeout(() => {
        setUserChangingFreq(false);
        console.log(`🔓 Flag boutons désactivé après 8 secondes`);
      }, 8000); // 8 secondes comme la molette

      console.log(`🔘 Bouton fréquence: ${formatFrequencyLCD(newFreq)} Hz (delta: ${delta}Hz, flag: ${userChangingFreq}, timestamp: ${Date.now()})`);
    }
  };

  const handleDirectFrequencySet = (freq) => {
    // Indiquer que l'utilisateur change la fréquence - AVEC TIMESTAMP
    setUserChangingFreq(true);
    setLastUserFreqChange(Date.now());

    // Mise à jour immédiate de l'affichage
    setFrequency(freq);

    // Envoi immédiat au récepteur
    sendCommand({ frequency: freq });

    // Mettre à jour les fréquences cibles
    setTargetFrequencies(prev =>
      prev.map(target => ({ ...target, active: target.freq === freq }))
    );

    // Arrêter le flag après un délai - TIMEOUT PLUS LONG POUR FRÉQUENCES CIBLES
    clearTimeout(window.userFreqTimeout);
    window.userFreqTimeout = setTimeout(() => {
      setUserChangingFreq(false);
    }, 7000); // 7 secondes pour fréquences cibles (plus long)

    console.log(`Fréquence cible: ${formatFrequencyLCD(freq)} Hz`);
  };

  // Gestionnaires pour les contrôles audio - SELON MANUEL ICOM IC-R8600
  const handleAfGainChange = useCallback((gain) => {
    setAfGain(gain);
    setRadioStatus(prev => ({ ...prev, af_gain: gain }));

    // Indiquer que l'utilisateur change l'AF Gain
    setUserChangingAF(true);

    // Debouncing pour éviter trop de commandes
    clearTimeout(window.afGainTimeout);
    window.afGainTimeout = setTimeout(() => {
      // COMMANDES AF GAIN - MÊME FORMAT QUE power_on QUI FONCTIONNE

      // 1. Commandes simples (format power_on)
      sendCommand({ af_gain: gain });
      sendCommand({ audio_gain: gain });
      sendCommand({ volume: gain });
      sendCommand({ af_level: gain });

      // 2. Commandes avec set_
      sendCommand({ set_af_gain: gain });
      sendCommand({ set_audio_gain: gain });
      sendCommand({ set_volume: gain });

      // 3. Commandes ICOM
      sendCommand({ icom_af_gain: gain });
      sendCommand({ ic_af_gain: gain });

      // 4. Commandes avec valeur 0-255 (comme ICOM)
      const gain255 = Math.round((gain / 100) * 255);
      sendCommand({ af_gain_255: gain255 });
      sendCommand({ audio_level_255: gain255 });

      // 5. Commandes CAT/CI-V
      const gainHex = gain255.toString(16).padStart(2, '0').toUpperCase();
      sendCommand({ cat_command: `AG0${gainHex}` });
      sendCommand({ civ_cmd: `1401${gainHex}` });

      console.log(`🔊 AF Gain: ${gain}% (${gain255}/255) → Toutes variantes envoyées`);

      // Arrêter le flag après envoi
      setTimeout(() => setUserChangingAF(false), 3000);
    }, 200);
  }, []);

  const handleRfGainChange = useCallback((gain) => {
    setRfGain(gain);
    setRadioStatus(prev => ({ ...prev, rf_gain: gain }));

    // Indiquer que l'utilisateur change le RF Gain
    setUserChangingRF(true);

    // Debouncing pour éviter trop de commandes
    clearTimeout(window.rfGainTimeout);
    window.rfGainTimeout = setTimeout(() => {
      // COMMANDES RF GAIN - MÊME FORMAT QUE power_on QUI FONCTIONNE

      // 1. Commandes simples (format power_on)
      sendCommand({ rf_gain: gain });
      sendCommand({ radio_gain: gain });
      sendCommand({ input_gain: gain });
      sendCommand({ rf_level: gain });

      // 2. Commandes avec set_
      sendCommand({ set_rf_gain: gain });
      sendCommand({ set_radio_gain: gain });
      sendCommand({ set_input_gain: gain });

      // 3. Commandes ICOM
      sendCommand({ icom_rf_gain: gain });
      sendCommand({ ic_rf_gain: gain });

      // 4. Commandes avec valeur 0-255 (comme ICOM)
      const gain255 = Math.round((gain / 100) * 255);
      sendCommand({ rf_gain_255: gain255 });
      sendCommand({ radio_level_255: gain255 });

      // 5. Commandes CAT/CI-V
      const gainHex = gain255.toString(16).padStart(2, '0').toUpperCase();
      sendCommand({ cat_command: `AG1${gainHex}` });
      sendCommand({ civ_cmd: `1402${gainHex}` });

      console.log(`📡 RF Gain: ${gain}% (${gain255}/255) → Toutes variantes envoyées`);

      // Arrêter le flag après envoi
      setTimeout(() => setUserChangingRF(false), 3000);
    }, 200);
  }, []);

  const handleModeChange = (newMode) => {
    setMode(newMode);
    setRadioStatus(prev => ({ ...prev, mode: newMode }));

    // Mapping spécial pour modes problématiques
    const modeMapping = {
      'D-STAR': ['DSTAR', 'D_STAR', 'DIGITAL', 'DV'],
      'FSK': ['FSK', 'RTTY', 'PSK', 'DATA'],
      'USB': ['USB', 'SSB_U'],
      'LSB': ['LSB', 'SSB_L']
    };

    // Essayer le mode direct d'abord
    sendCommand({ mode: newMode });
    sendCommand({ modulation: newMode });
    sendCommand({ demod: newMode });

    // Essayer les variantes spécifiques si disponibles
    if (modeMapping[newMode]) {
      modeMapping[newMode].forEach(variant => {
        sendCommand({ mode: variant });
        sendCommand({ modulation: variant });
        sendCommand({ demod: variant });
      });
    }

    console.log(`Mode changé: ${newMode} - Commandes multiples + variantes envoyées`);
  };

  const handleFilterChange = (filter) => {
    setFilterBW(filter);
    setRadioStatus(prev => ({ ...prev, filter: filter }));

    // Mapping des filtres selon MANUEL ICOM IC-R8600 EXACT
    const filterMapping = {
      'FIL1': {
        number: 1,
        code: 'NARROW',
        bw: 2400,
        icom_code: '01',
        cat_cmd: 'FI01'
      },
      'FIL2': {
        number: 2,
        code: 'MEDIUM',
        bw: 6000,
        icom_code: '02',
        cat_cmd: 'FI02'
      },
      'FIL3': {
        number: 3,
        code: 'WIDE',
        bw: 15000,
        icom_code: '03',
        cat_cmd: 'FI03'
      }
    };

    const filterConfig = filterMapping[filter];

    if (filterConfig) {
      // COMMANDES FILTRES - TESTER TOUTES LES VARIANTES POSSIBLES

      // 1. Commandes simples (comme power_on qui marche)
      sendCommand({ filter: filterConfig.number });
      sendCommand({ filter_bw: filterConfig.number });
      sendCommand({ bandwidth: filterConfig.bw });
      sendCommand({ bw: filterConfig.bw });

      // 2. Commandes avec préfixe
      sendCommand({ set_filter: filterConfig.number });
      sendCommand({ select_filter: filterConfig.number });
      sendCommand({ filter_select: filterConfig.number });
      sendCommand({ filter_number: filterConfig.number });

      // 3. Commandes par largeur
      sendCommand({ filter_width: filterConfig.bw });
      sendCommand({ bandwidth_hz: filterConfig.bw });
      sendCommand({ if_bandwidth: filterConfig.bw });

      // 4. Commandes texte
      sendCommand({ filter_mode: filterConfig.code });
      sendCommand({ filter_type: filterConfig.code });

      // 5. Commandes ICOM spécifiques
      sendCommand({ icom_filter: filterConfig.number });
      sendCommand({ ic_filter: filterConfig.number });

      // 6. Commandes CAT/CI-V
      sendCommand({ cat_command: filterConfig.cat_cmd });
      sendCommand({ civ_cmd: `1A05${filterConfig.icom_code}` });

      console.log(`🔧 Filtre ${filter}: Toutes variantes envoyées - Numéro: ${filterConfig.number}, BW: ${filterConfig.bw}Hz`);
    }
  };

  // Gestionnaires pour le scan - SELON MANUEL ICOM IC-R8600
  const startScan = async () => {
    // Validation des paramètres avec debug
    const startFreq = parseInt(scanStartFreq) || 144000000;
    const endFreq = parseInt(scanEndFreq) || 146000000;
    const stepFreq = parseInt(scanStep) || 25000;

    console.log(`🔍 SCAN ICOM - Valeurs entrées: Start=${scanStartFreq}, End=${scanEndFreq}, Step=${scanStep}`);
    console.log(`🔍 SCAN ICOM - Valeurs converties: Start=${startFreq}, End=${endFreq}, Step=${stepFreq}`);

    // Validation logique
    if (startFreq >= endFreq) {
      showMessage('error', 'Fréquence de début doit être inférieure à la fréquence de fin');
      return;
    }

    if (stepFreq <= 0 || stepFreq > (endFreq - startFreq)) {
      showMessage('error', 'Pas de fréquence invalide (doit être > 0 et < plage)');
      return;
    }

    if (startFreq < 100000 || endFreq > 3000000000) {
      showMessage('error', 'Fréquences hors limites (100kHz - 3GHz)');
      return;
    }

    setLoading(true);
    try {
      // COMMANDES SCAN ICOM IC-R8600 SELON MANUEL

      // 1. Arrêter scan en cours
      sendCommand({ scan_stop: true });
      sendCommand({ scan: 'stop' });
      sendCommand({ cat_command: 'SC00' });

      // 2. Programmer fréquence de début (selon manuel ICOM)
      sendCommand({ scan_start_freq: startFreq });
      sendCommand({ scan_frequency_start: startFreq });
      sendCommand({ freq_scan_start: startFreq });
      sendCommand({ cat_command: `SF${startFreq.toString(16).padStart(10, '0').toUpperCase()}` });

      // 3. Programmer fréquence de fin (selon manuel ICOM)
      sendCommand({ scan_end_freq: endFreq });
      sendCommand({ scan_frequency_end: endFreq });
      sendCommand({ freq_scan_end: endFreq });
      sendCommand({ cat_command: `SE${endFreq.toString(16).padStart(10, '0').toUpperCase()}` });

      // 4. Programmer le pas (selon manuel ICOM)
      sendCommand({ scan_step: stepFreq });
      sendCommand({ scan_frequency_step: stepFreq });
      sendCommand({ freq_scan_step: stepFreq });
      sendCommand({ cat_command: `SS${stepFreq.toString(16).padStart(8, '0').toUpperCase()}` });

      // 5. Programmer le mode
      const validScanModes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM', 'CWR', 'RTTY', 'RTTYR', 'PSK', 'PSKR'];
      const modeMapping = {
        'D-STAR': 'FM',
        'DIGITAL': 'FM',
        'FSK': 'RTTY',
        'UNKNOWN': 'FM'
      };
      const scanMode = validScanModes.includes(mode) ? mode : (modeMapping[mode] || 'FM');

      sendCommand({ scan_mode: scanMode });
      sendCommand({ mode: scanMode });

      // 6. Démarrer le scan (selon manuel ICOM)
      sendCommand({ scan_start: true });
      sendCommand({ scan: 'start' });
      sendCommand({ cat_command: 'SC01' }); // CAT command pour démarrer scan
      sendCommand({ civ_cmd: '0E01' }); // CI-V command pour scan

      // 7. Commandes API compatibles
      const scanData = {
        start_frequency: startFreq,
        end_frequency: endFreq,
        step: stepFreq,
        mode: scanMode,
        scan_type: 'programmed',
        scan_direction: 'up'
      };

      console.log(`🔍 SCAN ICOM - Données complètes envoyées:`, scanData);

      // Essayer l'API standard aussi
      try {
        const response = await axios.post(`${API_BASE_URL}/api/scan/start`, scanData);
        console.log(`✅ API SCAN réponse:`, response.data);
      } catch (apiError) {
        console.log(`⚠️ API SCAN erreur (normal si pas supportée):`, apiError.message);
      }

      setIsScanning(true);
      showMessage('success', 'Scan ICOM programmé et démarré');
      console.log(`🔍 SCAN ICOM démarré: ${formatFrequencyLCD(startFreq)} → ${formatFrequencyLCD(endFreq)} (pas: ${stepFreq}Hz, mode: ${scanMode})`);

    } catch (error) {
      console.error('❌ Erreur scan ICOM:', error);
      const errorMsg = error.response?.data?.detail ||
                      error.response?.data?.message ||
                      `Erreur scan ICOM (${error.response?.status || 'réseau'})`;
      showMessage('error', errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const stopScan = async () => {
    try {
      // COMMANDES ARRÊT SCAN ICOM IC-R8600 SELON MANUEL

      // 1. Commandes CAT pour arrêter scan
      sendCommand({ cat_command: 'SC00' }); // CAT: Arrêter scan
      sendCommand({ civ_cmd: '0E00' }); // CI-V: Arrêter scan

      // 2. Commandes directes
      sendCommand({ scan_stop: true });
      sendCommand({ scan: 'stop' });
      sendCommand({ scan_start: false });

      // 3. API standard
      try {
        const response = await axios.post(`${API_BASE_URL}/api/scan/stop`);
        console.log(`✅ API STOP SCAN réponse:`, response.data);
      } catch (apiError) {
        console.log(`⚠️ API STOP SCAN erreur (normal si pas supportée):`, apiError.message);
      }

      setIsScanning(false);
      showMessage('success', 'Scan ICOM arrêté');
      console.log(`🛑 SCAN ICOM arrêté`);

    } catch (error) {
      console.error('❌ Erreur arrêt scan ICOM:', error);
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt scan ICOM');
    }
  };

  // Gestionnaires pour l'audio streaming AUTOMATIQUE - DIAGNOSTIC COMPLET
  const startAudioStream = async () => {
    try {
      console.log('🔊 === DIAGNOSTIC AUDIO COMPLET ===');
      console.log('🔊 1. Démarrage streaming audio automatique');

      // DIAGNOSTIC 1: Vérifier support navigateur
      if (!window.AudioContext && !window.webkitAudioContext) {
        console.error('❌ ERREUR: AudioContext non supporté par ce navigateur');
        showMessage('error', 'AudioContext non supporté - Utilisez Chrome/Firefox');
        return;
      }

      // DIAGNOSTIC 2: Initialiser le contexte audio
      const context = new (window.AudioContext || window.webkitAudioContext)();
      audioContextRef.current = context;
      console.log(`🔊 2. Contexte audio créé - État: ${context.state}, Fréquence: ${context.sampleRate}Hz`);

      // DIAGNOSTIC 3: Reprendre le contexte audio si suspendu
      if (context.state === 'suspended') {
        console.log('🔊 3. Contexte suspendu - Tentative de reprise...');
        await context.resume();
        console.log(`🔊 3. Contexte repris - Nouvel état: ${context.state}`);
      }

      // DIAGNOSTIC 4: Créer les nœuds audio
      const analyser = context.createAnalyser();
      analyser.fftSize = 256;
      analyserRef.current = analyser;
      console.log('🔊 4. Analyseur audio créé');

      const gainNode = context.createGain();
      gainNode.gain.value = (volume / 100) * (isMuted ? 0 : 1);
      gainNodeRef.current = gainNode;
      console.log(`🔊 5. Nœud de gain créé - Valeur: ${gainNode.gain.value}`);

      // DIAGNOSTIC 5: Connecter la chaîne audio
      analyser.connect(gainNode);
      gainNode.connect(context.destination);
      console.log('🔊 6. Chaîne audio connectée: analyser → gain → haut-parleurs PC');

      // DIAGNOSTIC 6: Test audio immédiat
      console.log('🔊 7. Test audio des haut-parleurs...');
      testAudioOutput();

      console.log(`🔊 === DIAGNOSTIC TERMINÉ - Volume: ${volume}%, Muet: ${isMuted} ===`);

      // Établir la connexion WebSocket pour l'audio
      const ws = new WebSocket('ws://localhost:8001/ws/audio');
      websocketRef.current = ws;

      ws.onopen = () => {
        console.log('🔊 WebSocket audio connecté - STREAMING PERMANENT ACTIVÉ');
        setIsStreaming(true);
        showMessage('success', 'Audio récepteur connecté - Streaming permanent');

        // Test audio pour vérifier que les haut-parleurs fonctionnent
        testAudioOutput();

        // Demander le démarrage du streaming PERMANENT
        const streamRequest = {
          type: 'start_stream',
          continuous: true,
          auto_start: true,
          source: 'radio',
          format: 'pcm'
        };

        ws.send(JSON.stringify(streamRequest));
        console.log('🔊 STREAMING PERMANENT DEMANDÉ:', streamRequest);
        console.log('🔊 Attente données audio du récepteur...');

        // Timeout pour détecter si aucune donnée audio n'arrive
        setTimeout(() => {
          if (isStreaming && !window.audioDataReceived) {
            console.warn('⚠️ AUCUNE DONNÉE AUDIO REÇUE après 10s');
            console.warn('⚠️ Vérifiez: 1) Récepteur allumé, 2) Cable audio connecté, 3) Serveur backend');
            showMessage('warning', 'Aucune donnée audio reçue - Vérifiez récepteur et câble audio');
          }
        }, 10000);
      };

      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);

        console.log('🔊 === MESSAGE WEBSOCKET REÇU ===');
        console.log('🔊 Type message:', message.type);

        if (message.type === 'stream_started') {
          setIsStreaming(true);
          showMessage('success', 'Streaming audio du récepteur démarré');
          console.log('🔊 Stream démarré - Attente données audio...');
          startAudioAnalysis();
        } else if (message.type === 'audio_data') {
          // Marquer que des données audio sont reçues
          window.audioDataReceived = true;

          // Décoder et jouer les données audio du récepteur
          console.log('🔊 Données audio reçues - Taille:', message.data ? message.data.length : 0, 'caractères');
          if (message.data) {
            playAudioData(message.data);
          } else {
            console.warn('⚠️ Données audio vides reçues');
          }
        } else if (message.type === 'stream_stopped') {
          setIsStreaming(false);
          showMessage('info', 'Streaming audio arrêté');
          console.log('🔊 Stream arrêté');
        } else if (message.type === 'error') {
          console.error('❌ ERREUR SERVEUR AUDIO:', message.message);
          showMessage('error', `Erreur serveur: ${message.message}`);
        } else {
          console.log('🔊 Message non-audio:', message.type);
        }
      };

      ws.onclose = () => {
        console.log('🔊 WebSocket audio fermé - RECONNEXION AUTOMATIQUE dans 3s');
        setIsStreaming(false);
        websocketRef.current = null;

        // Reconnexion automatique pour maintenir l'audio permanent
        setTimeout(() => {
          console.log('🔊 TENTATIVE RECONNEXION AUDIO AUTOMATIQUE');
          ensureAudioStreaming();
        }, 3000);
      };

      ws.onerror = (error) => {
        console.error('❌ Erreur WebSocket audio:', error);
        showMessage('error', 'Erreur connexion audio - Vérifiez que le récepteur est connecté à la ligne d\'entrée');
        setIsStreaming(false);
      };

    } catch (error) {
      console.error('❌ Erreur démarrage streaming:', error);
      showMessage('error', 'Erreur démarrage streaming audio');
    }
  };

  // Fonction pour jouer les données audio - DIAGNOSTIC COMPLET
  const playAudioData = async (audioDataB64) => {
    try {
      console.log('🔊 === LECTURE AUDIO RÉCEPTEUR ===');
      console.log('🔊 1. Données reçues - Taille base64:', audioDataB64.length);

      // DIAGNOSTIC 1: Vérifier contexte audio
      if (!audioContextRef.current || !analyserRef.current || !gainNodeRef.current) {
        console.error('❌ ERREUR: Contexte audio non initialisé');
        console.log('❌ audioContext:', !!audioContextRef.current);
        console.log('❌ analyser:', !!analyserRef.current);
        console.log('❌ gainNode:', !!gainNodeRef.current);
        showMessage('error', 'Contexte audio manquant - Redémarrez la page');
        return;
      }

      // DIAGNOSTIC 2: Vérifier état contexte
      console.log(`🔊 2. État contexte: ${audioContextRef.current.state}`);
      if (audioContextRef.current.state === 'suspended') {
        console.log('🔊 2. Reprise contexte suspendu...');
        await audioContextRef.current.resume();
        console.log(`🔊 2. Contexte repris: ${audioContextRef.current.state}`);
      }

      // DIAGNOSTIC 3: Décoder les données audio base64
      console.log('🔊 3. Décodage base64...');
      const audioBytes = atob(audioDataB64);
      console.log('🔊 3. Bytes décodés:', audioBytes.length);

      const audioArray = new Int16Array(audioBytes.length / 2);
      for (let i = 0; i < audioArray.length; i++) {
        audioArray[i] = (audioBytes.charCodeAt(i * 2) & 0xFF) |
                       ((audioBytes.charCodeAt(i * 2 + 1) & 0xFF) << 8);
      }
      console.log('🔊 3. Samples Int16:', audioArray.length);

      // DIAGNOSTIC 4: Convertir en Float32Array
      const floatArray = new Float32Array(audioArray.length);
      for (let i = 0; i < audioArray.length; i++) {
        floatArray[i] = audioArray[i] / 32768.0; // Normaliser -1 à 1
      }
      console.log('🔊 4. Samples Float32:', floatArray.length);

      // Vérifier si les données ne sont pas silencieuses
      const maxSample = Math.max(...floatArray.map(Math.abs));
      console.log('🔊 4. Amplitude max:', maxSample.toFixed(4));
      if (maxSample < 0.001) {
        console.warn('⚠️ ATTENTION: Audio très faible ou silencieux');
      }

      // DIAGNOSTIC 5: Créer buffer audio
      const sampleRate = audioContextRef.current.sampleRate || 48000;
      console.log('🔊 5. Fréquence échantillonnage:', sampleRate, 'Hz');

      const audioBuffer = audioContextRef.current.createBuffer(1, floatArray.length, sampleRate);
      audioBuffer.getChannelData(0).set(floatArray);
      console.log('🔊 5. Buffer audio créé');

      // DIAGNOSTIC 6: Créer source et connecter
      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(analyserRef.current);
      console.log('🔊 6. Source connectée à analyser');

      // DIAGNOSTIC 7: Ajuster volume
      const finalGain = (volume / 100) * (isMuted ? 0 : 1);
      gainNodeRef.current.gain.value = finalGain;
      console.log(`🔊 7. Gain final: ${finalGain} (Volume: ${volume}%, Muet: ${isMuted})`);

      // DIAGNOSTIC 8: Jouer le son
      source.start();
      console.log('🔊 8. *** AUDIO JOUÉ SUR HAUT-PARLEURS PC ***');

      // Vérifier si l'audio est effectivement joué
      source.onended = () => {
        console.log('🔊 9. Lecture audio terminée');
      };

      showMessage('success', `Audio joué: ${floatArray.length} samples, gain: ${finalGain.toFixed(2)}`);

    } catch (error) {
      console.error('❌ ERREUR CRITIQUE LECTURE AUDIO:', error);
      showMessage('error', `Erreur audio: ${error.message}`);
    }
  };

  // Fonction de test audio RENFORCÉE pour diagnostic complet
  const testAudioOutput = () => {
    try {
      console.log('🔊 === TEST AUDIO HAUT-PARLEURS PC ===');

      if (!audioContextRef.current) {
        console.error('❌ ERREUR TEST: Contexte audio manquant');
        showMessage('error', 'Contexte audio non initialisé');
        return;
      }

      if (!gainNodeRef.current) {
        console.error('❌ ERREUR TEST: Nœud de gain manquant');
        showMessage('error', 'Nœud de gain non initialisé');
        return;
      }

      console.log(`🔊 TEST: Contexte état: ${audioContextRef.current.state}`);
      console.log(`🔊 TEST: Gain valeur: ${gainNodeRef.current.gain.value}`);
      console.log(`🔊 TEST: Volume: ${volume}%, Muet: ${isMuted}`);

      // Créer un oscillateur pour test (bip court et fort)
      const oscillator = audioContextRef.current.createOscillator();
      oscillator.frequency.setValueAtTime(1000, audioContextRef.current.currentTime); // 1000Hz
      oscillator.type = 'sine';

      // Créer un gain temporaire pour le test (BEAUCOUP PLUS FORT)
      const testGain = audioContextRef.current.createGain();
      testGain.gain.value = 0.8; // Volume test très fort pour être sûr d'entendre

      // Connecter: oscillateur → gain test → gain principal → haut-parleurs
      oscillator.connect(testGain);
      testGain.connect(gainNodeRef.current);

      // Jouer un bip de test TRÈS LONG et TRÈS FORT
      oscillator.start();
      oscillator.stop(audioContextRef.current.currentTime + 2.0); // 2 secondes

      console.log('🔊 TEST: Bip 1000Hz/2s TRÈS FORT joué - Vous devriez l\'entendre !');
      showMessage('warning', '🔊 TEST AUDIO FORT: Bip 2s joué ! Si vous n\'entendez rien, vérifiez volume PC !');

      // Vérifier les périphériques audio
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        navigator.mediaDevices.enumerateDevices().then(devices => {
          const audioOutputs = devices.filter(device => device.kind === 'audiooutput');
          console.log('🔊 Périphériques audio détectés:', audioOutputs.length);
          audioOutputs.forEach((device, index) => {
            console.log(`🔊 Audio ${index + 1}: ${device.label || 'Périphérique inconnu'}`);
          });
        });
      }

      // Test supplémentaire après 1 seconde
      setTimeout(() => {
        try {
          const oscillator2 = audioContextRef.current.createOscillator();
          oscillator2.frequency.setValueAtTime(500, audioContextRef.current.currentTime);
          oscillator2.type = 'square';

          const testGain2 = audioContextRef.current.createGain();
          testGain2.gain.value = 0.2;

          oscillator2.connect(testGain2);
          testGain2.connect(gainNodeRef.current);

          oscillator2.start();
          oscillator2.stop(audioContextRef.current.currentTime + 0.3);

          console.log('🔊 TEST 2: Bip 500Hz/300ms joué - Deuxième test');
        } catch (error) {
          console.error('❌ ERREUR TEST 2:', error);
        }
      }, 1000);

    } catch (error) {
      console.error('❌ ERREUR TEST AUDIO CRITIQUE:', error);
      showMessage('error', `Erreur test audio: ${error.message}`);
    }
  };

  const stopAudioStream = () => {
    // Arrêter le WebSocket
    if (websocketRef.current) {
      websocketRef.current.send(JSON.stringify({
        type: 'stop_stream'
      }));
      websocketRef.current.close();
      websocketRef.current = null;
    }

    // Fermer le contexte audio
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setIsStreaming(false);
    showMessage('success', 'Streaming audio arrêté');
  };

  const startAudioAnalysis = () => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const analyze = () => {
      analyser.getByteFrequencyData(dataArray);
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      setAudioLevel(Math.round((average / 255) * 100));

      if (isStreaming) {
        requestAnimationFrame(analyze);
      }
    };

    analyze();
  };

  // Gestionnaires pour l'enregistrement
  const startRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: 'AF'
      });
      setIsRecording(true);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur enregistrement');
    }
  };

  const stopRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsRecording(false);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt enregistrement');
    }
  };

  // Gestionnaires pour les fréquences cibles
  const addTargetFrequency = () => {
    if (newFreqInput) {
      const freq = parseInt(newFreqInput);
      if (freq >= 100000 && freq <= 3000000000) {
        setTargetFrequencies(prev => [...prev, {
          freq: freq,
          name: `F${prev.length + 1}`,
          comment: newFreqComment || 'Nouvelle fréquence',
          active: false
        }]);
        setNewFreqInput('');
        setNewFreqComment('');
        showMessage('success', 'Fréquence cible ajoutée');
      }
    }
  };

  const removeTargetFrequency = (index) => {
    setTargetFrequencies(prev => prev.filter((_, i) => i !== index));
    showMessage('success', 'Fréquence cible supprimée');
  };

  // Gestionnaires pour l'alimentation - COMMANDE FONCTIONNELLE IDENTIFIÉE
  const handlePowerOn = () => {
    // Initialiser l'audio au premier clic (Chrome policy)
    initializeAudioOnUserGesture();

    // SEULE COMMANDE QUI FONCTIONNE selon les logs
    sendCommand({ power_on: true });
    console.log('🔌 Commande POWER ON fonctionnelle envoyée: {power_on: true}');
  };

  const handlePowerOff = () => {
    // Initialiser l'audio au premier clic (Chrome policy)
    initializeAudioOnUserGesture();

    // COMMANDE POWER OFF - TESTER PLUSIEURS VARIANTES
    sendCommand({ power_on: false });
    sendCommand({ power_off: true });
    sendCommand({ power: false });
    sendCommand({ power: 'off' });
    sendCommand({ pwr: 0 });
    console.log('🔌 Commandes POWER OFF envoyées - Testant plusieurs variantes');
  };

  // Gestionnaires pour l'audio streaming - AUTOMATIQUE PERMANENT
  const ensureAudioStreaming = () => {
    if (!isStreaming) {
      console.log('🔊 RECONNEXION AUDIO AUTOMATIQUE');
      startAudioStream();
    } else {
      console.log('🔊 Audio déjà actif - Streaming permanent');
    }
  };

  // Fonction pour initialiser l'audio au premier clic utilisateur (Chrome policy)
  const initializeAudioOnUserGesture = () => {
    if (!audioInitialized) {
      console.log('🔊 INITIALISATION AUDIO AU PREMIER CLIC UTILISATEUR');
      setAudioInitialized(true);
      startAudioStream();
    }
  };

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };



  // Gestionnaire pour la molette de la souris - corrigé
  useEffect(() => {
    const handleWheel = (e) => {
      // Vérifier si la souris est sur la molette rotative
      const knobElement = document.querySelector('.rotary-knob');
      if (knobElement && knobElement.contains(e.target)) {
        e.preventDefault();
        e.stopPropagation();
        const delta = e.deltaY > 0 ? -1 : 1;
        handleKnobRotation(delta);
        return false;
      }
    };

    // Ajouter l'événement avec { passive: false } pour permettre preventDefault
    document.addEventListener('wheel', handleWheel, { passive: false, capture: true });

    return () => {
      document.removeEventListener('wheel', handleWheel, { capture: true });
    };
  }, [handleKnobRotation]);

  // Effet pour charger les données au démarrage - ARRÊT COMPLET DE LA SYNC AUTO
  useEffect(() => {
    console.log(`🚫 SYNCHRONISATION AUTOMATIQUE DÉSACTIVÉE - Interface MAÎTRE absolu`);

    // Lecture initiale seulement pour les gains/mode (PAS la fréquence)
    const initializeOtherValues = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/status`);
        const newStatus = response.data;

        // Synchroniser SEULEMENT les valeurs non-fréquence
        if (newStatus.af_gain !== undefined) setAfGain(newStatus.af_gain);
        if (newStatus.rf_gain !== undefined) setRfGain(newStatus.rf_gain);
        if (newStatus.mode) setMode(newStatus.mode);

        console.log(`✅ INIT - Gains/Mode synchronisés, Fréquence locale MAÎTRE: ${formatFrequencyLCD(frequency)} Hz`);

        // Forcer le récepteur à suivre la fréquence locale
        setTimeout(() => {
          console.log(`🎯 FORCER RÉCEPTEUR: ${formatFrequencyLCD(frequency)} Hz`);
          sendCommand({ frequency: frequency });
        }, 1000);

      } catch (error) {
        console.error('Erreur init:', error);
      }
    };

    initializeOtherValues();

    // AUDIO SERA DÉMARRÉ AU PREMIER CLIC UTILISATEUR (Chrome policy)
    console.log('🔊 Audio sera démarré au premier clic utilisateur (Chrome policy)');

    // PLUS D'INTERVALLE AUTOMATIQUE - Synchronisation manuelle seulement

    // Nettoyage lors du démontage (plus d'interval à nettoyer)
    return () => {
      console.log(`🧹 Nettoyage composant - Plus d'interval à nettoyer`);
      if (websocketRef.current) {
        websocketRef.current.close();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Styles pour les boutons
  const buttonStyle = {
    base: {
      padding: '8px 16px',
      border: 'none',
      borderRadius: '6px',
      fontSize: '0.9rem',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      justifyContent: 'center',
      boxShadow: c2ewTheme.shadows.button,
      textTransform: 'uppercase',
      letterSpacing: '0.5px'
    },
    primary: {
      background: c2ewTheme.colors.accent,
      color: 'white'
    },
    success: {
      background: c2ewTheme.colors.success,
      color: 'white'
    },
    danger: {
      background: c2ewTheme.colors.danger,
      color: 'white'
    },
    warning: {
      background: c2ewTheme.colors.warning,
      color: 'white'
    },
    secondary: {
      background: c2ewTheme.colors.darkBorder,
      color: c2ewTheme.colors.text
    },
    active: {
      background: c2ewTheme.colors.success,
      color: 'white',
      boxShadow: `0 0 10px ${c2ewTheme.colors.success}`
    }
  };

  return (
    <div style={styles.container}>
      {/* Messages d'alerte compacts */}
      {message.text && (
        <div style={{
          padding: '8px 12px',
          borderRadius: '4px',
          marginBottom: '10px',
          fontSize: '0.8rem',
          backgroundColor: message.type === 'success' ?
            'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
          borderLeft: `3px solid ${message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger}`,
          color: message.type === 'success' ?
            c2ewTheme.colors.success : c2ewTheme.colors.danger
        }}>
          {message.text}
        </div>
      )}

      {/* Grille principale compacte */}
      <div style={styles.mainGrid}>

        {/* Écran LCD et contrôles de fréquence */}
        <div style={styles.card}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            <h2 style={{...styles.cardTitle, margin: 0}}>
              <Monitor size={16} />
              LCD - Fréquence
            </h2>

            {/* Boutons ON/OFF dans la même ligne */}
            <div style={{
              display: 'flex',
              gap: '8px'
            }}>
              <button
                onClick={handlePowerOn}
                disabled={loading || radioStatus.power_on}
                style={{
                  width: '45px',
                  height: '22px',
                  borderRadius: '11px',
                  border: `2px solid ${radioStatus.power_on ? c2ewTheme.colors.success : c2ewTheme.colors.darkBorder}`,
                  background: radioStatus.power_on ? c2ewTheme.colors.success : 'transparent',
                  color: radioStatus.power_on ? 'white' : c2ewTheme.colors.success,
                  fontSize: '0.65rem',
                  cursor: radioStatus.power_on ? 'default' : 'pointer',
                  opacity: radioStatus.power_on ? 1 : 0.8,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease',
                  boxShadow: radioStatus.power_on ? `0 0 8px ${c2ewTheme.colors.success}40` : 'none'
                }}
              >
                <Power size={10} style={{ marginRight: '2px' }} />
                ON
              </button>
              <button
                onClick={handlePowerOff}
                disabled={loading || !radioStatus.power_on}
                style={{
                  width: '45px',
                  height: '22px',
                  borderRadius: '11px',
                  border: `2px solid ${!radioStatus.power_on ? c2ewTheme.colors.danger : c2ewTheme.colors.darkBorder}`,
                  background: !radioStatus.power_on ? c2ewTheme.colors.danger : 'transparent',
                  color: !radioStatus.power_on ? 'white' : c2ewTheme.colors.danger,
                  fontSize: '0.65rem',
                  cursor: !radioStatus.power_on ? 'default' : 'pointer',
                  opacity: !radioStatus.power_on ? 1 : 0.8,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease',
                  boxShadow: !radioStatus.power_on ? `0 0 8px ${c2ewTheme.colors.danger}40` : 'none'
                }}
              >
                <Power size={10} style={{ marginRight: '2px' }} />
                OFF
              </button>
              <button
                onClick={syncGainsOnly}
                style={{
                  width: '45px',
                  height: '22px',
                  borderRadius: '11px',
                  border: `2px solid ${c2ewTheme.colors.warning}`,
                  background: 'transparent',
                  color: c2ewTheme.colors.warning,
                  fontSize: '0.6rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease'
                }}
                title="Synchroniser gains/mode"
              >
                📡 SYNC
              </button>
              <button
                onClick={() => {
                  console.log('🔊 TEST AUDIO MANUEL DEMANDÉ');
                  initializeAudioOnUserGesture();
                  testAudioOutput();
                }}
                style={{
                  width: '45px',
                  height: '22px',
                  borderRadius: '11px',
                  border: `2px solid ${c2ewTheme.colors.info}`,
                  background: 'transparent',
                  color: c2ewTheme.colors.info,
                  fontSize: '0.6rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease'
                }}
                title="Test audio haut-parleurs PC"
              >
                🔊 TEST
              </button>
            </div>
          </div>

          {/* Écran LCD éditable avec boutons ON/OFF intégrés */}
          <div style={styles.lcdDisplay}>
            {isEditingFreq ? (
              <div>
                <input
                  type="text"
                  value={frequencyInput}
                  onChange={(e) => setFrequencyInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') handleFrequencySubmit();
                    if (e.key === 'Escape') handleFrequencyCancel();
                  }}
                  style={styles.frequencyInput}
                  autoFocus
                  placeholder="000.000.000"
                />
                <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                  <button
                    onClick={handleFrequencySubmit}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.success,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✓ VALIDER
                  </button>
                  <button
                    onClick={handleFrequencyCancel}
                    style={{
                      ...buttonStyle.base,
                      ...buttonStyle.danger,
                      padding: '4px 12px',
                      fontSize: '0.7rem'
                    }}
                  >
                    ✗ ANNULER
                  </button>
                </div>
              </div>
            ) : (
              <div style={{ position: 'relative' }}>
                <div onClick={handleFrequencyEdit} style={{ cursor: 'pointer' }}>
                  <div style={styles.lcdFrequency}>
                    {formatFrequencyLCD(frequency)}
                  </div>
                  <div style={styles.lcdInfo}>
                    <span>{radioStatus.mode || mode}</span>
                    <span>RSSI: {radioStatus.rssi || -80} dBm</span>
                    <span style={{
                      color: radioStatus.power_on ? c2ewTheme.colors.success : c2ewTheme.colors.danger,
                      fontWeight: 'bold'
                    }}>
                      {radioStatus.power_on ? 'ON' : 'OFF'}
                    </span>
                  </div>
                  <div style={{
                    fontSize: '0.7rem',
                    color: c2ewTheme.colors.textMuted,
                    marginTop: '3px',
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}>
                    <span>AF: {radioStatus.af_gain || afGain}%</span>
                    <span>RF: {radioStatus.rf_gain || rfGain}%</span>
                    {commandInProgress && (
                      <span style={{ color: c2ewTheme.colors.warning }}>⏳</span>
                    )}
                  </div>
                  <div style={{
                    fontSize: '0.7rem',
                    color: c2ewTheme.colors.textMuted,
                    marginTop: '5px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <span>Cliquez pour éditer la fréquence</span>
                    <span style={{
                      fontSize: '0.6rem',
                      color: userChangingFreq ? c2ewTheme.colors.accent :
                             frequency === radioStatus.frequency ? c2ewTheme.colors.success : c2ewTheme.colors.warning
                    }}>
                      {userChangingFreq ? '🎛️ MOLETTE' :
                       frequency === radioStatus.frequency ? '✓ SYNC' : '⟳ SYNC...'}
                    </span>
                  </div>
                </div>


              </div>
            )}
          </div>

          {/* Molette rotative et contrôles gains */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr auto 1fr',
            gap: '15px',
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            {/* AF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  AF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {radioStatus.af_gain || afGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={radioStatus.af_gain || afGain}
                onChange={(e) => handleAfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Molette centrale */}
            <div style={{ textAlign: 'center' }}>
              <div style={{
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '8px'
              }}>
                MOLETTE FRÉQ
              </div>
              <div
                className="rotary-knob"
                style={{
                  ...styles.rotaryKnob,
                  transform: `rotate(${knobRotation}deg)`
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  const rect = e.currentTarget.getBoundingClientRect();
                  const centerX = rect.left + rect.width / 2;
                  const clickX = e.clientX;
                  const delta = clickX > centerX ? 1 : -1;
                  handleKnobRotation(delta);
                }}
              >
                <div style={styles.rotaryIndicator}></div>
              </div>
              <div style={{
                fontSize: '0.7rem',
                color: c2ewTheme.colors.textMuted
              }}>
                ±10Hz
              </div>
            </div>

            {/* RF Gain */}
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.8rem' }}>
                  RF GAIN
                </span>
                <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.8rem' }}>
                  {radioStatus.rf_gain || rfGain}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={radioStatus.rf_gain || rfGain}
                onChange={(e) => handleRfGainChange(parseInt(e.target.value))}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>
          </div>

          {/* Contrôles rapides de fréquence */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '4px',
              marginBottom: '6px'
            }}>
              <button
                onClick={() => handleFrequencyChange(-100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -100k
              </button>
              <button
                onClick={() => handleFrequencyChange(-25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -25k
              </button>
              <button
                onClick={() => handleFrequencyChange(-10000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                -10k
              </button>
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '4px'
            }}>
              <button
                onClick={() => handleFrequencyChange(10000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +10k
              </button>
              <button
                onClick={() => handleFrequencyChange(25000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +25k
              </button>
              <button
                onClick={() => handleFrequencyChange(100000)}
                style={{...buttonStyle.base, ...buttonStyle.secondary, padding: '4px 6px', fontSize: '0.7rem'}}
                disabled={loading}
              >
                +100k
              </button>
            </div>
          </div>

          {/* Audio Live Récepteur */}
          <div style={{ marginTop: '10px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              AUDIO LIVE RÉCEPTEUR
            </div>

            {/* Contrôle Volume PC avec MUTE */}
            <div style={{ marginBottom: '10px' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '5px'
              }}>
                <span style={{ color: c2ewTheme.colors.textMuted, fontSize: '0.7rem' }}>
                  VOLUME PC
                </span>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <button
                    onClick={() => {
                      setIsMuted(!isMuted);
                      if (gainNodeRef.current) {
                        gainNodeRef.current.gain.value = (volume / 100) * (!isMuted ? 0 : 1);
                        console.log(`🔇 Mute: ${!isMuted}`);
                      }
                    }}
                    style={{
                      ...buttonStyle.base,
                      ...(isMuted ? buttonStyle.danger : buttonStyle.secondary),
                      padding: '2px 6px',
                      fontSize: '0.6rem'
                    }}
                  >
                    {isMuted ? '🔇' : '🔊'}
                  </button>
                  <span style={{ color: c2ewTheme.colors.text, fontWeight: 'bold', fontSize: '0.7rem' }}>
                    {isMuted ? 'MUTE' : `${volume}%`}
                  </span>
                </div>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => {
                  const newVolume = parseInt(e.target.value);
                  setVolume(newVolume);
                  // Mettre à jour le gain audio en temps réel
                  if (gainNodeRef.current) {
                    gainNodeRef.current.gain.value = (newVolume / 100) * (isMuted ? 0 : 1);
                    console.log(`🔊 Volume changé: ${newVolume}%, Gain: ${gainNodeRef.current.gain.value}`);
                  }
                }}
                style={{
                  width: '100%',
                  height: '4px',
                  borderRadius: '2px',
                  background: c2ewTheme.colors.darkBorder,
                  outline: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Audio Status et Enregistrement - AUDIO AUTOMATIQUE */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '8px'
            }}>
              <div style={{
                color: isStreaming ? c2ewTheme.colors.success : c2ewTheme.colors.danger,
                fontSize: '0.7rem',
                fontWeight: 'bold'
              }}>
                🔊 AUDIO: {isStreaming ? 'ACTIF' : 'INACTIF'}
              </div>
              <button
                onClick={toggleRecording}
                style={{
                  ...buttonStyle.base,
                  ...(isRecording ? buttonStyle.danger : buttonStyle.secondary),
                  padding: '4px 8px',
                  fontSize: '0.6rem'
                }}
              >
                <Square size={10} />
                {isRecording ? 'STOP' : 'REC'}
              </button>
            </div>
          </div>

          {/* Modulation - Déplacée sous LIVE AUDIO */}
          <div style={{ marginTop: '15px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              MODULATION
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '4px'
            }}>
              {modes.slice(0, 8).map(m => (
                <button
                  key={m}
                  onClick={() => handleModeChange(m)}
                  style={{
                    ...buttonStyle.base,
                    ...(mode === m ? buttonStyle.active : buttonStyle.secondary),
                    padding: '4px 6px',
                    fontSize: '0.7rem'
                  }}
                  disabled={loading}
                >
                  {m}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Contrôles Audio et Filtres */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Volume2 size={16} />
            Audio & Filtres
          </h2>

          {/* Filtres BW - Comme sur le récepteur réel */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              FILTRES BW
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '4px'
            }}>
              {[
                { name: 'FIL1', bw: '2.4k' },
                { name: 'FIL2', bw: '6k' },
                { name: 'FIL3', bw: '15k' }
              ].map((filter, index) => (
                <button
                  key={filter.name}
                  onClick={() => handleFilterChange(filter.name)}
                  style={{
                    ...buttonStyle.base,
                    ...(filterBW === filter.name ? buttonStyle.active : buttonStyle.secondary),
                    padding: '4px 6px',
                    fontSize: '0.7rem',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center'
                  }}
                  disabled={loading}
                >
                  <div>{filter.name}</div>
                  <div style={{ fontSize: '0.6rem', opacity: 0.8 }}>{filter.bw}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Fréquences Cibles - Déplacées en haut */}
          <div>
            <div style={{
              color: c2ewTheme.colors.textMuted,
              fontSize: '0.8rem',
              marginBottom: '8px'
            }}>
              FRÉQUENCES CIBLES
            </div>

            {/* Ajout rapide de fréquence avec commentaire */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '4px',
              marginBottom: '8px'
            }}>
              <input
                type="text"
                value={newFreqInput}
                onChange={(e) => setNewFreqInput(e.target.value)}
                placeholder="145500000"
                style={{
                  padding: '4px 6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.darkCard,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.7rem'
                }}
              />
              <input
                type="text"
                value={newFreqComment}
                onChange={(e) => setNewFreqComment(e.target.value)}
                placeholder="Commentaire - ex: Canal d'urgence"
                style={{
                  padding: '4px 6px',
                  border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                  borderRadius: '4px',
                  background: c2ewTheme.colors.darkCard,
                  color: c2ewTheme.colors.text,
                  fontSize: '0.7rem'
                }}
              />
              <button
                onClick={addTargetFrequency}
                style={{
                  ...buttonStyle.base,
                  ...buttonStyle.success,
                  padding: '4px 8px',
                  fontSize: '0.7rem'
                }}
              >
                <Plus size={10} />
                AJOUTER
              </button>
            </div>

            {/* Liste compacte des fréquences */}
            <div style={{
              maxHeight: '120px',
              overflowY: 'auto',
              border: `1px solid ${c2ewTheme.colors.darkBorder}`,
              borderRadius: '4px',
              background: c2ewTheme.colors.dark
            }}>
              {targetFrequencies.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  color: c2ewTheme.colors.textMuted,
                  padding: '8px',
                  fontSize: '0.7rem'
                }}>
                  Aucune fréquence
                </div>
              ) : (
                targetFrequencies.map((target, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '4px 6px',
                      borderBottom: index < targetFrequencies.length - 1 ?
                        `1px solid ${c2ewTheme.colors.darkBorder}` : 'none',
                      background: target.active ?
                        c2ewTheme.colors.success + '20' : 'transparent',
                      cursor: 'pointer',
                      fontSize: '0.7rem'
                    }}
                    onClick={() => handleDirectFrequencySet(target.freq)}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <div style={{
                          color: target.active ? c2ewTheme.colors.success : c2ewTheme.colors.text,
                          fontWeight: 'bold'
                        }}>
                          {formatFrequency(target.freq)}
                        </div>
                        <div style={{
                          color: c2ewTheme.colors.textMuted,
                          fontSize: '0.6rem'
                        }}>
                          {target.comment}
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeTargetFrequency(index);
                        }}
                        style={{
                          ...buttonStyle.base,
                          ...buttonStyle.danger,
                          padding: '2px 4px',
                          fontSize: '0.6rem'
                        }}
                      >
                        <Trash2 size={8} />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Section SCAN PROGRAMMABLE */}
            <div style={{ marginTop: '12px' }}>
              <div style={{
                color: c2ewTheme.colors.textMuted,
                fontSize: '0.8rem',
                marginBottom: '8px'
              }}>
                SCAN PROGRAMMABLE
              </div>

              {/* Champs avec labels */}
              <div style={{ marginBottom: '6px' }}>
                <div style={{
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.6rem',
                  marginBottom: '2px'
                }}>
                  DÉBUT (Hz)
                </div>
                <input
                  type="number"
                  value={scanStartFreq}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 144000000;
                    setScanStartFreq(value);
                    console.log(`SCAN Start Freq changé: ${value}`);
                  }}
                  placeholder="144000000"
                  style={{
                    width: '100%',
                    padding: '4px 6px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.7rem'
                  }}
                />
              </div>

              <div style={{ marginBottom: '6px' }}>
                <div style={{
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.6rem',
                  marginBottom: '2px'
                }}>
                  FIN (Hz)
                </div>
                <input
                  type="number"
                  value={scanEndFreq}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 146000000;
                    setScanEndFreq(value);
                    console.log(`SCAN End Freq changé: ${value}`);
                  }}
                  placeholder="146000000"
                  style={{
                    width: '100%',
                    padding: '4px 6px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.7rem'
                  }}
                />
              </div>

              <div style={{ marginBottom: '8px' }}>
                <div style={{
                  color: c2ewTheme.colors.textMuted,
                  fontSize: '0.6rem',
                  marginBottom: '2px'
                }}>
                  PAS (Hz)
                </div>
                <input
                  type="number"
                  value={scanStep}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 25000;
                    setScanStep(value);
                    console.log(`SCAN Step changé: ${value}`);
                  }}
                  placeholder="25000"
                  style={{
                    width: '100%',
                    padding: '4px 6px',
                    border: `1px solid ${c2ewTheme.colors.darkBorder}`,
                    borderRadius: '4px',
                    background: c2ewTheme.colors.darkCard,
                    color: c2ewTheme.colors.text,
                    fontSize: '0.7rem'
                  }}
                />
              </div>

              {/* Boutons de contrôle */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '4px'
              }}>
                <button
                  onClick={startScan}
                  disabled={loading || isScanning}
                  style={{
                    ...buttonStyle.base,
                    ...(isScanning ? buttonStyle.active : buttonStyle.success),
                    padding: '4px 8px',
                    fontSize: '0.7rem'
                  }}
                >
                  <Play size={10} />
                  {isScanning ? 'SCANNING...' : 'START'}
                </button>
                <button
                  onClick={stopScan}
                  disabled={loading || !isScanning}
                  style={{
                    ...buttonStyle.base,
                    ...buttonStyle.danger,
                    padding: '4px 8px',
                    fontSize: '0.7rem'
                  }}
                >
                  <Square size={10} />
                  STOP
                </button>
              </div>

              {/* Indicateur de statut */}
              {isScanning && (
                <div style={{
                  marginTop: '6px',
                  padding: '4px',
                  background: c2ewTheme.colors.warning,
                  color: 'white',
                  borderRadius: '3px',
                  fontSize: '0.6rem',
                  textAlign: 'center'
                }}>
                  🔍 SCAN EN COURS...
                </div>
              )}
            </div>
          </div>


        </div>

      </div>




      {/* Styles CSS intégrés */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }

          input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
          }

          input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-moz-range-thumb {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${c2ewTheme.colors.accent};
            cursor: pointer;
            border: 1px solid ${c2ewTheme.colors.text};
          }

          input[type="range"]::-webkit-slider-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }

          input[type="range"]::-moz-range-track {
            background: ${c2ewTheme.colors.darkBorder};
            height: 4px;
            border-radius: 2px;
          }
        `
      }} />
    </div>
  );
};

export default C2EWRadioControl;
